﻿using System.Diagnostics;
using System.Net;

namespace ItemScraper
{
    public static class Network
    {
        public static int DownloadedUrls;
        private static HttpClient HttpClient;
        private static CookieContainer CookieContainer;

        static Network()
        {
            CookieContainer = new CookieContainer();
            HttpClient = new HttpClient(new HttpClientHandler
            {
                CookieContainer = CookieContainer,
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
            });
            HttpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36");
            HttpClient.DefaultRequestHeaders.Accept.ParseAdd("text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
            HttpClient.DefaultRequestHeaders.AcceptLanguage.ParseAdd("en-us,en;q=0.5");
            HttpClient.DefaultRequestHeaders.AcceptEncoding.ParseAdd("gzip,deflate");
            HttpClient.DefaultRequestHeaders.AcceptCharset.ParseAdd("utf-8;q=0.7,*;q=0.7");
        }

        public static async Task<string> FetchUrlAsync(string url)
        {
            for (var i = 0; i < 3; i++)
            {
                try
                {
                    var response = await HttpClient.GetAsync(url);
                    if (response.IsSuccessStatusCode)
                    {
                        var result = await response.Content.ReadAsStringAsync();
                        if (!string.IsNullOrEmpty(result))
                        {
                            return result;
                        }
                    }
                }
                catch (HttpRequestException ex)
                {
                    Debug.WriteLine($"FetchUrlAsync Exception: {ex.Message}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Unexpected Exception: {ex.Message}");
                }
            }

            return string.Empty;
        }
    }
}
