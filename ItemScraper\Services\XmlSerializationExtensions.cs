﻿using System.Text;
using System.Xml;
using System.Xml.Serialization;
using System.Collections.Concurrent;

namespace ItemScraper.Services;

public static class XmlSerializationExtensions
{
    private static readonly ConcurrentDictionary<(Type, string), XmlSerializer> SerializerCache = new();

    public static string SerializeXmlWithNamespace<T>(this T obj, string defaultNamespace) where T : class
    {
        if (obj == null)
        {
            throw new ArgumentNullException(nameof(obj));
        }

        if (string.IsNullOrWhiteSpace(defaultNamespace))
        {
            throw new ArgumentException("Namespace cannot be null or empty", nameof(defaultNamespace));
        }

        var xmlWriterSettings = new XmlWriterSettings
        {
            Indent = true,
            IndentChars = "  ",
            Encoding = Encoding.UTF8,
            OmitXmlDeclaration = false
        };

        var serializer = GetOrCreateSerializer(typeof(T), defaultNamespace);
        var ns = new XmlSerializerNamespaces();
        ns.Add(string.Empty, defaultNamespace);

        using var stringWriter = new StringWriter();
        using var xmlWriter = XmlWriter.Create(stringWriter, xmlWriterSettings);

        try
        {
            serializer.Serialize(xmlWriter, obj, ns);
            return stringWriter.ToString();
        }
        catch (InvalidOperationException ex)
        {
            throw new XmlSerializationException($"Failed to serialize object of type {typeof(T).Name}", ex);
        }
    }

    public static T DeserializeXmlWithNamespace<T>(string xml, string defaultNamespace) where T : class
    {
        if (string.IsNullOrWhiteSpace(xml))
        {
            throw new ArgumentException("XML string cannot be null or empty", nameof(xml));
        }

        if (string.IsNullOrWhiteSpace(defaultNamespace))
        {
            throw new ArgumentException("Namespace cannot be null or empty", nameof(defaultNamespace));
        }

        var serializer = GetOrCreateSerializer(typeof(T), defaultNamespace);

        using var stringReader = new StringReader(xml);
        using var xmlReader = new CustomXmlTextReader(stringReader, defaultNamespace);

        try
        {
            return (T)serializer.Deserialize(xmlReader);
        }
        catch (InvalidOperationException ex)
        {
            throw new XmlSerializationException($"Failed to deserialize XML to type {typeof(T).Name}", ex);
        }
    }

    private static XmlSerializer GetOrCreateSerializer(Type type, string defaultNamespace)
    {
        return SerializerCache.GetOrAdd((type, defaultNamespace), key =>
        {
            var (objectType, ns) = key;
            return new XmlSerializer(objectType, new XmlRootAttribute { Namespace = ns });
        });
    }
}

public class CustomXmlTextReader : XmlTextReader
{
    private readonly string _defaultNamespace;
    private readonly XmlReaderSettings _settings;

    public CustomXmlTextReader(TextReader reader, string defaultNamespace) : base(reader)
    {
        _defaultNamespace = defaultNamespace ?? throw new ArgumentNullException(nameof(defaultNamespace));

        _settings = new XmlReaderSettings
        {
            IgnoreWhitespace = true,
            IgnoreComments = true,
            IgnoreProcessingInstructions = true,
            DtdProcessing = DtdProcessing.Ignore, // Security best practice
            ValidationType = ValidationType.None
        };
    }

    public override string NamespaceURI
    {
        get
        {
            var baseNamespace = base.NamespaceURI;
            return string.IsNullOrEmpty(baseNamespace) ? _defaultNamespace : baseNamespace;
        }
    }

    public XmlReaderSettings Settings => _settings;
}

public class XmlSerializationException : Exception
{
    public XmlSerializationException(string message) : base(message)
    {
    }

    public XmlSerializationException(string message, Exception innerException)
        : base(message, innerException)
    {
    }
}
