using ItemScraper.Configuration;
using ItemScraper.Services.Interfaces;
using ItemScraper.Terms;

namespace ItemScraper.Services
{
    public class ScrapingService : IScrapingService, IDisposable
    {
        private readonly ScraperConfig _config;
        private readonly LoggingService _logger;
        private readonly ItemProcessor _itemProcessor;
        private readonly ISerpProcessor _serpProcessor;
        private readonly int _maxDegreeOfParallelism;
        private bool _disposed;

        public ScrapingService(
            ScraperConfig config,
            LoggingService logger,
            ItemProcessor itemProcessor,
            ISerpProcessor serpProcessor,
            int maxDegreeOfParallelism)
        {
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _itemProcessor = itemProcessor ?? throw new ArgumentNullException(nameof(itemProcessor));
            _serpProcessor = serpProcessor ?? throw new ArgumentNullException(nameof(serpProcessor));
            _maxDegreeOfParallelism = maxDegreeOfParallelism; // Assign parameter to field
        }

        public async Task Initialize()
        {
            try
            {
                await _itemProcessor.Initialize();
            }
            catch (Exception ex)
            {
                _logger.LogError("Error initializing scraping service", ex);
                throw;
            }
        }

        public async Task ProcessTerms(int lastTermIndex)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ScrapingService));

            try
            {
                var terms = TermManager.ImportTermsFromCsv(_config.SearchTermsFile);
                LogStats.TotalTerms = terms.Count; // Set total terms count

                // Get the terms we still need to process
                var termsToProcess = terms.Skip(lastTermIndex).ToList();

                // Configure parallel options - adjust MaxDegreeOfParallelism as needed
                var parallelOptions = new ParallelOptions
                {
                    MaxDegreeOfParallelism = _maxDegreeOfParallelism
                };

                // Track the current index for logging
                int currentIndex = lastTermIndex;

                await Parallel.ForEachAsync(
                    termsToProcess,
                    parallelOptions,
                    async (term, cancellationToken) =>
                    {
                        int termIndex;

                        // Thread-safe update of the current index for display/logging
                        lock (this)
                        {
                            termIndex = currentIndex++;
                            LogStats.CurrentTermIndex = termIndex; // Update current term index
                            LogStats.CurrentCategoryId = term.CategoryId;
                        }

                        // Set up for this specific term
                        ScraperConfig.DataAnalysisTableName = $"_{Program.DataAnalysisDateStated}_{term.Alias.Replace(" ", "_")}";

                        // Process the term
                        await Initialize();
                        await ProcessTerm(term);

                        // Save the progress - note each thread will update its own progress
                        await SaveTermIndex(termIndex);
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError("Error processing terms", ex);
                throw;
            }
        }

        private async Task ProcessTerm(SearchTerm term)
        {
            term.MaxCategoryStartDateToSave = DateTime.MinValue;

            await ReadEndTimesDict(term);

            var lastStartDatePath = Path.Combine("start_dates", $"{term.CategoryId}.txt");
            if (File.Exists(lastStartDatePath))
            {
                var startDate = await File.ReadAllTextAsync(lastStartDatePath);
                term.MinLastStartDateTarget = DateTime.Parse(startDate);
            }
            else if (term.MinLastStartDateTarget == DateTime.MinValue)
            {
                term.MinLastStartDateTarget = DateTime.UtcNow.AddDays(_config.DaysBack);
            }
            Console.WriteLine(term.MinLastStartDateTarget);
            var serpUrlsLists = new List<List<string>> { SerpUrlGenerator.GenerateSerpUrls(term.BasicSerpUrl) };
            var serpUrlsListIndex = 0;

            while (serpUrlsListIndex < serpUrlsLists.Count)
            {
                LogStats.TotalUrlSets = serpUrlsLists.Count; // Set initial total URL sets
                LogStats.CurrentUrlSetIndex = serpUrlsListIndex; // Update current URL set index
                await ProcessUrlList(serpUrlsListIndex, term, serpUrlsLists);
                serpUrlsListIndex++;
            }
            if (term.MaxCategoryStartDateToSave != DateTime.MinValue)
                await File.WriteAllTextAsync(lastStartDatePath, term.MaxCategoryStartDateToSave.AddDays(-1).ToString("yyyy-MM-dd"));


            await SaveEndTimesDict(term);
        }

        private static async Task ReadEndTimesDict(SearchTerm term)
        {
            term.DateItemDict = new();
            var categoryPath = Path.Combine("end_dates", $"{term.CategoryId}");
            if (Directory.Exists(categoryPath))
            {
                var endDateFiles = Directory.GetFiles(categoryPath);
                foreach (var file in endDateFiles)
                {
                    var date = Path.GetFileNameWithoutExtension(file);
                    var lines = await File.ReadAllLinesAsync(file);
                    term.DateItemDict[date] = lines.Select(long.Parse).ToHashSet();
                }
            }
        }

        private static async Task SaveEndTimesDict(SearchTerm term)
        {
            var categoryPath = Path.Combine("end_dates", $"{term.CategoryId}");
            if (!Directory.Exists(categoryPath))
            {
                Directory.CreateDirectory(categoryPath);
            }

            foreach (var kvPair in term.DateItemDict)
            {
                //Save values into txt file names. Names are keys. Subfolder is end_times/{term.CategoryId}/{date}.txt

                var endDateFilePath = Path.Combine(categoryPath, $"{kvPair.Key}.txt");


                await File.WriteAllLinesAsync(endDateFilePath, kvPair.Value.Select(id=>id.ToString()).ToList());
            }
        }

        private async Task ProcessUrlList(int listIndex, SearchTerm term, List<List<string>> serpUrlsLists)
        {
            var urlIndex = 0;

            while (urlIndex < serpUrlsLists[listIndex].Count)
            {
                _logger.LogProgress();

                try
                {
                    var continueProcessing = await _serpProcessor.ProcessSerpUrl(serpUrlsLists[listIndex][urlIndex], term, serpUrlsLists);
                    if (!continueProcessing) break;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error processing SERP URL: {serpUrlsLists[listIndex][urlIndex]}", ex);
                }

                urlIndex++;
            }
        }

        public async Task<int> LoadLastTermIndex()
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ScrapingService));

            try
            {
                string dateFileName = $"SearchLastTermIndex_{DateTime.UtcNow:yyyyMMdd}.txt";
                if (File.Exists(dateFileName))
                {
                    var savedIndex = await File.ReadAllTextAsync(dateFileName);
                    if (int.TryParse(savedIndex, out var lastSavedIndex))
                    {
                        return lastSavedIndex;
                    }
                }
                return 0;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error loading last term index", ex);
                return 0;
            }
        }

        private async Task SaveTermIndex(int termIndex)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ScrapingService));

            try
            {
                string dateFileName = $"SearchLastTermIndex_{DateTime.UtcNow:yyyyMMdd}.txt";
                await File.WriteAllTextAsync(dateFileName, termIndex.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error saving term index: {termIndex}", ex);
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _itemProcessor.Dispose();
                _serpProcessor.Dispose();
                _disposed = true;
            }
        }
    }
}
