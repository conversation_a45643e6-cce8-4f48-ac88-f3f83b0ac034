using ItemScraper.Configuration;
using ItemScraper.Services.Interfaces;
using ItemScraper.Terms;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace ItemScraper.Services
{
    public class SerpProcessor : ISerpProcessor
    {
        private readonly object _consoleLock = new object();
        private int _serpUrlsDownloaded = 0;
        private int _itemDownloadListTotal = 0;
        private readonly ScraperConfig _config;
        private readonly ItemProcessor _itemProcessor;
        private bool _disposed;
        private ItemValidator _itemValidator;

        public int SerpUrlsDownloaded => _serpUrlsDownloaded;
        public int ItemDownloadListTotal => _itemDownloadListTotal;

        public SerpProcessor(ScraperConfig config, ItemProcessor itemProcessor, ItemValidator itemValidator)
        {
            _itemValidator = itemValidator;
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _itemProcessor = itemProcessor ?? throw new ArgumentNullException(nameof(itemProcessor));
        }

        public async Task<bool> ProcessSerpUrl(string serpUrl, SearchTerm term, List<List<string>> serpUrlsLists)
        {
            try
            {
                var availableInventoryFix = serpUrl.Contains("_udlo") ? serpUrl : serpUrl + "&_udlo=0";
                var htmlContent = await Network.FetchUrlAsync(availableInventoryFix);
                _serpUrlsDownloaded++;
                LogStats.TotalUrlsDownloaded = _serpUrlsDownloaded; // Sync with LogStats

                var estimatedResultCount = SerpUrlGenerator.ParseSerpItemsCount(htmlContent);
                if (estimatedResultCount > SerpUrlGenerator.EbayMaxResultsCount)
                {
                    if (htmlContent.Contains("data-enable-before") || htmlContent.Contains("data-enable-after"))
                    {
                        await HandleDateRangeSplit(serpUrl, term.BasicSerpUrl, serpUrlsLists);
                    }
                    else
                    {
                        await HandlePriceRangeSplit(htmlContent, term.BasicSerpUrl, serpUrlsLists);
                    }

                    return false;
                }

                var serpItems = SerpParser.ParseIdTimeFromSerp(htmlContent);
                var hasNextPage = await ProcessSerpItems(serpItems, term);
                if (htmlContent.Contains("class=\"pagination__next icon-btn\" aria-disabled=true"))
                {
                    return false;
                }
                if (!htmlContent.Contains("<nav role=navigation class=pagination"))
                {
                    return false;
                }


                return hasNextPage;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing SERP URL: {ex}");
                return true; // Continue to next URL in case of error
            }
        }

        public async Task HandlePriceRangeSplit(string htmlContent, string basicSerpUrl, List<List<string>> serpUrlsLists)
        {
            var elements = SerpUrlGenerator.ParseHtmlToPriceRanges(htmlContent);
            var aggregatedRanges = SerpUrlGenerator.AggregatePriceRanges(elements);

            foreach (var aggregatedRange in aggregatedRanges)
            {
                if (aggregatedRange.Count <= SerpUrlGenerator.EbayMaxResultsCount)
                {
                    serpUrlsLists.Add(SerpUrlGenerator.GenerateSerpUrls(basicSerpUrl, aggregatedRange.Min, aggregatedRange.Max));
                }
                else
                {
                    var generateSerpUrlsInPriceRange = SerpUrlGenerator.GenerateSerpUrlsInPriceRange(aggregatedRange.Min, aggregatedRange.Max, basicSerpUrl);
                    serpUrlsLists.AddRange(generateSerpUrlsInPriceRange);
                }
            }
        }

        public async Task HandleDateRangeSplit(string serpUrl, string basicSerpUrl, List<List<string>> serpUrlsLists)
        {
            var minPrice = decimal.Parse(SerpUrlGenerator.RegexValue(serpUrl, "_udlo=([0-9\\.]+)"));
            var maxPrice = decimal.Parse(SerpUrlGenerator.RegexValue(serpUrl, "_udhi=([0-9\\.]+)"));

            if (minPrice <= maxPrice)
            {
                serpUrlsLists.AddRange(SerpUrlGenerator.GenerateSerpUrlsInPriceRange(minPrice, maxPrice, basicSerpUrl));
            }
        }

        public async Task<bool> ProcessSerpItems(List<SerpParser.SerpItem> serpItems, SearchTerm term)
        {
            if (serpItems == null || !serpItems.Any())
                return true;

            var hasNextPage = true;

            List<SerpParser.SerpItem> itemsInDateRange;
            if (_config.AnalysisType == AnalysisType.DataAnalysis)
            {
                var startDate = term.DateEndEarliest;
                var endDate = term.DateEndLatest;
                itemsInDateRange = serpItems.Where(item => item.Date >= startDate && item.Date <= endDate).ToList();
                hasNextPage = itemsInDateRange.Any();
                if (!hasNextPage)
                {

                }
            }
            else
            {
                // Check if we should continue to next page based on date range
                var indexToCheckFrom = (int)Math.Ceiling(serpItems.Count * 0.75) - 1;
                var skippedToSearchOldItems = serpItems.Skip(indexToCheckFrom);
                hasNextPage = skippedToSearchOldItems.Any(item => item.Date >= term.MinLastStartDateTarget);

                var unseenItems = _itemValidator.KeepUnseenItems(serpItems).ToArray().ToList();
                itemsInDateRange = unseenItems.Where(item => item.Date >= term.MinLastStartDateTarget).ToList();
                //doesn't contain item id from previous days
                itemsInDateRange = itemsInDateRange.Where(item => term.DateItemDict.All(dict => !dict.Value.Contains(item.ItemID))).ToList();
                foreach (var serpItem in serpItems)
                {
                    if ((DateTime.UtcNow - serpItem.Date).TotalDays > 6)
                    {
                        continue;
                    } 
                    if (!term.DateItemDict.ContainsKey(serpItem.Date.ToString("yyyy-MM-dd")))
                        term.DateItemDict.TryAdd(serpItem.Date.ToString("yyyy-MM-dd"), [serpItem.ItemID]);
                    else
                        term.DateItemDict[serpItem.Date.ToString("yyyy-MM-dd")].Add(serpItem.ItemID);
                }
            }

            if (itemsInDateRange.Count == 0)
                return false;

            // Process the items
            await _itemProcessor.ProcessBatch(itemsInDateRange, term);
            _itemDownloadListTotal += itemsInDateRange.Count;
            if (!hasNextPage)

                return false;
            return true;
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _disposed = true;
            }
        }
    }
}
