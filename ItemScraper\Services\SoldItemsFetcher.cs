using Npgsql;
using ItemScraper.Configuration;

namespace ItemScraper.Services
{
    public class SoldItemsFetcher
    {
        private readonly DatabaseConfiguration _dbConfig;

        public SoldItemsFetcher()
        {
            _dbConfig = DatabaseConfigurations.GetConfiguration("SoldItems");
        }

        public async Task<List<long>> FetchSoldItemIds(List<int> categoryIds)
        {
            var itemIds = new List<long>();
            var ninetyDaysAgo = DateTime.UtcNow.AddDays(-30);

            using (var connection = new NpgsqlConnection(_dbConfig.GetConnectionString()))
            {
                await connection.OpenAsync();

                var sql = @"
                    SELECT item_id 
                    FROM public.sold_items 
                    WHERE leaf_category_id = ANY(@categoryIds)
                    AND end_time >= @ninetyDaysAgo
                    --AND duration < 900
                    ORDER BY end_time DESC";

                using (var cmd = new NpgsqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("categoryIds", categoryIds);
                    cmd.Parameters.AddWithValue("ninetyDaysAgo", ninetyDaysAgo);

                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            itemIds.Add(reader.GetInt64(0));
                        }
                    }
                }
            }

            return itemIds;
        }

        public async Task<List<int>> GetLeafCategoryIds(int categoryId)
        {
            var leafCategories = new HashSet<int>();

            using (var connection = new NpgsqlConnection(_dbConfig.GetConnectionString()))
            {
                await connection.OpenAsync();

                var sql = @"
                    WITH RECURSIVE category_tree AS (
                        SELECT id, id_path
                        FROM public.categories
                        WHERE id = @categoryId

                        UNION ALL

                        SELECT c.id, c.id_path
                        FROM public.categories c
                        INNER JOIN category_tree ct ON c.id_path LIKE ct.id_path || '  |  %'
                    )
                    SELECT id
                    FROM category_tree ct1
                    WHERE NOT EXISTS (
                        SELECT 1
                        FROM public.categories c2
                        WHERE c2.id_path LIKE ct1.id_path || '  |  %'
                    )";

                using (var cmd = new NpgsqlCommand(sql, connection))
                {
                    cmd.Parameters.AddWithValue("categoryId", categoryId);

                    using (var reader = await cmd.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            leafCategories.Add(reader.GetInt32(0));
                        }
                    }
                }
            }

            return leafCategories.Count > 0 ? leafCategories.ToList() : [categoryId];
        }
    }
}