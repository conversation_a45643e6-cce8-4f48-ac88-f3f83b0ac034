﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PublishAot>false</PublishAot>
    <InvariantGlobalization>true</InvariantGlobalization>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="old_project_files\**" />
    <EmbeddedResource Remove="old_project_files\**" />
    <None Remove="old_project_files\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="eBay.Service.Fork" Version="4.0.1-pre" />
    <PackageReference Include="HtmlAgilityPack" Version="1.11.71" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="NetJSON" Version="1.4.4" />
    <PackageReference Include="Npgsql" Version="8.0.5" />
  </ItemGroup>

</Project>
