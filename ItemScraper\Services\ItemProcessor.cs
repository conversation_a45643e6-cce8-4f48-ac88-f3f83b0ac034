using System.Collections.Concurrent;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using ItemScraper.Configuration;
using ItemScraper.Terms;
using uBuyFirst.API.ShoppingAPI;

namespace ItemScraper.Services
{
    public class ItemProcessor
    {
        private readonly ScraperConfig _config;
        private readonly LoggingService _logger;
        private readonly ApiContext _apiContext;
        private readonly ItemDetailsFactory _itemDetailsFactory;
        private readonly SemaphoreSlim _processingLock;
        private DataAnalysisToDB _dataAnalysisToDb;
        private bool _disposed;
        private ItemValidator _itemValidator;

        public ItemProcessor(ScraperConfig config, LoggingService logger, ApiContext apiContext,
            ItemValidator itemValidator)
        {
            _itemValidator = itemValidator;
            _config = config ?? throw new ArgumentNullException(nameof(config));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _apiContext = apiContext ?? throw new ArgumentNullException(nameof(apiContext));
            _itemDetailsFactory = new ItemDetailsFactory(_config.AnalysisType);
            _processingLock = new SemaphoreSlim(1, 1);

        }

        public async Task Initialize()
        {
            await _processingLock.WaitAsync();
            try
            {
                ValidateConfiguration();

                if (_config.TradingApiEnabled)
                {
                    _logger.LogMessage("Initializing Trading API database tables");
                }

                if (_config.AnalysisType == AnalysisType.DataAnalysis)
                // Initialize ItemPredict table
                {
                    _dataAnalysisToDb = new DataAnalysisToDB(ScraperConfig.DataAnalysisTableName);
                    await _dataAnalysisToDb.CreateTableIfNotExists();
                }
            }
            finally
            {
                _processingLock.Release();
            }
        }

        public ItemPredictData CreateItemPredictData(ItemType item, DateTime fetchTime, string searchTerm, SerpParser.SerpItem serpItem)
        {
            var p = new ItemPredictData();
            try
            {
                if (item.Title == null)
                {
                    return null;
                }
                p.Title = item.Title;
                p.BestOffer = item.BestOfferDetails?.BestOfferEnabled ?? false;
                p.BidCount = item.BiddingDetails?.QuantityBid ?? 0;

                p.BuyerProtection = item.BuyerProtection.ToString();
                p.CategoryID = int.Parse(item.PrimaryCategory.CategoryID);
                p.ConditionID = item.ConditionID;
                p.ConditionDescription = item.ConditionDescription;
                p.CountryFrom = item.Country.ToString();
                p.CountryShipsTo = string.Join(",", item.ShipToLocations);
                p.Description = TextProcessor.StripHTML(HtmlToText.ConvertHtml(item.Description));
                p.DispatchTimeMax = item.DispatchTimeMax;
                p.SellerFeedbackCount = item.Seller.FeedbackScore;
                p.SellerFeedbackPercent = item.Seller.PositiveFeedbackPercent;
                p.SellerFeedbackPrivate = item.Seller.FeedbackPrivate;
                p.BuyerName = item.SellingStatus?.HighBidder?.UserID?.Replace("***", "");
                p.BuyerFeedbackCount = item.SellingStatus?.HighBidder?.FeedbackScore;
                p.BuyerFeedbackPercent = item.SellingStatus?.HighBidder?.PositiveFeedbackPercent;
                if (item.SellingStatus?.HighBidder?.FeedbackScore == null && !string.IsNullOrEmpty(p.BuyerName))
                {
                    p.BuyerFeedbackCount = 0;
                    p.BuyerFeedbackPrivate = true;
                }

                p.GetItFast = item.GetItFast;
                p.ItemID = long.Parse(item.ItemID);
                p.ListingType = item.ListingType;
                p.LotSize = item.LotSize;
                p.PicturesURL = string.Join(";", item.PictureDetails?.PictureURL ?? []);
                p.PicturesCount = item.PictureDetails?.PictureURL?.Length ?? 0;

                p.PostalCode = item.PostalCode?.Trim('*') ?? "";
                p.QuantityTotal = item.Quantity;
                p.QuantitySold = item.SellingStatus.QuantitySold;
                p.RefundOption = item.ReturnPolicy?.RefundOption;
                p.RelistParentID = item.RelistParentID;
                p.ReturnsAcceptedOption = item.ReturnPolicy?.ReturnsAcceptedOption;
                p.ReturnsWithinOption = item.ReturnPolicy?.ReturnsWithinOption;
                p.Revised = item.ReviseStatus.ItemRevised;
                p.SellerName = item.Seller.UserID;
                p.ShippingCostPaidByOption = item.ReturnPolicy?.ShippingCostPaidByOption;
                p.SubTitle = item.SubTitle;
                p.TimeEnded = item.ListingDetails?.EndTime;
                p.TimePosted = item.ListingDetails?.StartTime;
                p.TopRatedListing = item.TopRatedListing;
                p.SoldAsBin = item.SellingStatus.SoldAsBin;
                p.BestOfferSold = serpItem.BestOfferSold;
                p.DurationSeconds = (decimal)Math.Round((item.ListingDetails.EndTime - item.ListingDetails.StartTime).TotalSeconds, 0);
                p.Price = ItemDetailsFactory.GetItemPrice(item);
                p.BINSold = item.SellingStatus.QuantitySold > 0 &&
                            (item.ListingType == ListingTypeCodeType.FixedPriceItem ||
                             ((item.ListingType == ListingTypeCodeType.Auction ||
                               item.ListingType == ListingTypeCodeType.Chinese) && item.SellingStatus.SoldAsBin));
                p.AuctionSold = item.SellingStatus.QuantitySold > 0 &&
                                (item.ListingType == ListingTypeCodeType.Auction ||
                                 item.ListingType == ListingTypeCodeType.Chinese) &&
                                item.SellingStatus.SoldAsBin == false;
                p.BINNotSold = item.SellingStatus.QuantitySold == 0 &&
                               item.ListingType == ListingTypeCodeType.FixedPriceItem;
                p.AuctionNotSold = item.SellingStatus.QuantitySold == 0 &&
                                   (item.ListingType == ListingTypeCodeType.Auction ||
                                    item.ListingType == ListingTypeCodeType.Chinese);
                p.CommitToBuy = !item.AutoPay;
                p.Comment = "";
                p.ShippingType = item.ShippingDetails.ShippingType;

                p.ShippingPrice = (decimal)(item.ShippingDetails.ShippingServiceOptions?.FirstOrDefault()
                    ?.ShippingServiceCost?.Value ?? 0);
                if (serpItem.ShippingPrice > p.ShippingPrice)
                {
                    p.ShippingPrice = serpItem.ShippingPrice;
                }

                if (p.ShippingPrice > 999)
                    p.ShippingPrice = 999;

                p.Specifics = new Dictionary<string, string>();
                p.SearchName = searchTerm;

                var pidsToRemove = new[] { "does not apply", "doesnotapply", "not applicable", "notapplicable", "n/a", "na" };
                bool IsValidPid(string pid) => pid != null && !pidsToRemove.Contains(pid.ToLower());
                string GetValidPid(string pid) => IsValidPid(pid) ? pid : null;

                string SanitizeName(string name) =>
                    name.Replace(" ", "_")
                        .Replace("(", "")
                        .Replace(")", "")
                        .Replace("/", "")
                        .Replace(".", "_")
                        .Replace("!", "")
                        .Replace(":", "")
                        .Trim('.')
                        .Trim();

                void AddSpecific(Dictionary<string, string> specifics, string name, string value)
                {
                    if (!specifics.ContainsKey(name))
                        specifics.Add(name, value);
                }

                // Assign basic product details
                p.Brand = GetValidPid(item.ProductListingDetails?.BrandMPN?.Brand);
                p.MPN = GetValidPid(item.ProductListingDetails?.BrandMPN?.MPN);
                p.UPC = GetValidPid(item.ProductListingDetails?.UPC);

                p.ProductReferenceID = GetValidPid(item.ProductListingDetails?.ProductReferenceID);

                // Process item specifics
                if (item.ItemSpecifics != null)
                {
                    foreach (var specific in item.ItemSpecifics)
                    {
                        if (specific.Name == "Brand" || specific.Name == "MPN" || specific.Name == "UPC" || specific.Name == "ProductReferenceID") continue;
                        if (specific.Value != null)
                        {
                            var filteredValues = specific.Value.Where(IsValidPid).ToArray();

                            if (filteredValues.Any())
                            {
                                var sanitizedName = SanitizeName(specific.Name);
                                if (specific.Name == "Model")
                                    p.Model = string.Join(",", filteredValues);
                                else
                                    AddSpecific(p.Specifics, sanitizedName, string.Join(",", filteredValues));
                            }
                        }
                    }
                }

                // Add additional product listing details
                if (item.ProductListingDetails != null)
                {
                    if (IsValidPid(item.ProductListingDetails.UPC))
                        AddSpecific(p.Specifics, "ProductUPC", item.ProductListingDetails.UPC);

                    if (IsValidPid(item.ProductListingDetails.ISBN))
                        AddSpecific(p.Specifics, "ProductISBN", item.ProductListingDetails.ISBN);

                    if (IsValidPid(item.ProductListingDetails.EAN))
                        AddSpecific(p.Specifics, "ProductEAN", item.ProductListingDetails.EAN);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return p;
        }

        public async Task ProcessSoldTradingItem(SerpParser.SerpItem serpItem, ConcurrentDictionary<long, ItemDetails> validSoldItemsDict, ConcurrentDictionary<long, InvalidItem> invalidItems)
        {
            try
            {
                var getItemTuple = await GetItems.GetItemsDetails(_apiContext, serpItem, _config.AnalysisType);

                if (getItemTuple == null || getItemTuple.Item1.ListingDetails == null)
                {
                    InvalidItem invalidItem = new() { ItemId = serpItem.ItemID, FetchTime = DateTime.UtcNow };
                    invalidItems.TryAdd(serpItem.ItemID, invalidItem);
                    _itemValidator.AddInvalidItem(serpItem.ItemID);
                    return;
                }

                if (!_itemValidator.ValidateItemDetails(getItemTuple.Item1))
                {
                    InvalidItem invalidItem = new() { ItemId = serpItem.ItemID, FetchTime = DateTime.UtcNow };
                    invalidItems.TryAdd(serpItem.ItemID, invalidItem);
                    _itemValidator.AddInvalidItem(serpItem.ItemID);
                }

                ItemDetails validItem = _itemDetailsFactory.CreateSoldItemDetails(getItemTuple.Item1);

                if (validItem != null)
                {
                    _itemValidator.AddValidItem(validItem.item_id);
                    LogStats.TotalItemsFound++; // Increment total items found when a valid item is processed
                    validSoldItemsDict.TryAdd(validItem.item_id, validItem);
                }
                else
                {
                    try
                    {
                        await File.AppendAllTextAsync("deserializationError.txt", $"{serpItem.ItemID}\n");
                    }
                    catch (Exception e)
                    {
                        _logger.LogError("Error writing to deserializationError.txt", e);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error processing item {serpItem.ItemID}", ex);
            }
        }
        //romadebug
        public static bool UseShoppingAPI = false;
        private static readonly object _objLock = new object();
        public async Task ProcessBatch(List<SerpParser.SerpItem> serpItems, SearchTerm term)
        {
            if (_disposed) throw new ObjectDisposedException(nameof(ItemProcessor));
            if (serpItems == null) throw new ArgumentNullException(nameof(serpItems));

            LogStats.TotalItems = serpItems.Count; // Set total items for this batch

            var semaphore = new SemaphoreSlim(_config.ThreadCount);
            var tasks = new List<Task>();
            var validSoldItemsDict = new ConcurrentDictionary<long, ItemDetails>();

            var itemPredictList = new ConcurrentDictionary<string, ItemPredictData>();
            var invalidItemsDict = new ConcurrentDictionary<long, InvalidItem>();
            try
            {
                if (UseShoppingAPI)
                {
                    var fetchedItems = new ConcurrentBag<ShoppingAPIJson.SimpleItemType>();
                    //romadebug
                    var cutOffDate = DateTime.ParseExact("2025-02-11", "yyyy-MM-dd", System.Globalization.CultureInfo.InvariantCulture);
                    var itemIds = serpItems.Where(serpItem => serpItem.Date > cutOffDate).Select(i => i.ItemID).ToList();

                    for (int i = 0; i < itemIds.Count; i += 20)
                    {

                        await semaphore.WaitAsync();
                        var i1 = i;
                        tasks.Add(Task.Run(async () =>
                            {
                                try
                                {
                                    var batch = itemIds.Skip(i1).Take(20).ToList();
                                    var batchResult = await ShoppingClient.SimpleItemCallAsync(batch);
                                    lock (_objLock)
                                    {
                                        if (batchResult?.Response?.Item != null)
                                        {
                                            for (var index = 0; index < batchResult?.Response?.Item?.Length; index++)
                                            {
                                                fetchedItems.Add(batchResult.Response.Item[index]);
                                            }
                                        }
                                    }
                                }
                                catch (Exception e)
                                {
                                    Console.WriteLine(e);

                                }
                                finally
                                {
                                    semaphore.Release();
                                }
                            }
                        ));
                    }
                    await Task.WhenAll(tasks);
                    for (var index = 0; index < fetchedItems.Count; index++)
                    {
                        var simpleItem = fetchedItems.ElementAt(index);
                        if (simpleItem == null)
                        {
                            continue;
                        }

                        var shoppingDetails = ShoppingClient.CreateSoldItemDetails(simpleItem);
                        var itemId = long.Parse(simpleItem.ItemID);
                        _itemValidator.AddValidItem(itemId);
                        LogStats.TotalItemsFound++; // Increment total items found when a valid item is processed
                        validSoldItemsDict.TryAdd(itemId, shoppingDetails);
                    }

                    var fetchFailedItems = itemIds.Except(fetchedItems.Select(item => long.Parse(item.ItemID))).ToList();
                    foreach (var fetchFailedItem in fetchFailedItems)
                    {
                        InvalidItem invalidItem = new()
                        { ItemId = fetchFailedItem, FetchTime = DateTime.UtcNow };
                        invalidItemsDict.TryAdd(fetchFailedItem, invalidItem);
                    }
                }


                if (!UseShoppingAPI)
                {
                    for (var itemIndex = 0; itemIndex < serpItems.Count; itemIndex++)
                    {
                        LogStats.CurrentItemIndex = itemIndex + 1; // Update current item index
                        var serpItem = serpItems[itemIndex];


                        await semaphore.WaitAsync();

                        tasks.Add(Task.Run(async () =>
                        {
                            try
                            {
                                if (itemIndex % 1 == 0)
                                {
                                    // Update progress with actual statistics
                                    _logger.LogProgress();
                                }

                                if (_config.AnalysisType == AnalysisType.SoldItems)
                                {
                                    await ProcessSoldTradingItem(serpItem, validSoldItemsDict, invalidItemsDict);
                                }

                                if (_config.AnalysisType == AnalysisType.DataAnalysis)
                                {
                                    // Get the original eBay item data and create ItemPredictData
                                    var getItemTuple = await GetItems.GetItemsDetails(_apiContext, serpItem, _config.AnalysisType);
                                    if (getItemTuple != null)
                                    {
                                        var itemPredictData = CreateItemPredictData(getItemTuple.Item1, getItemTuple.Item2, term?.Alias, serpItem);
                                        if (itemPredictData != null)
                                        {
                                            itemPredictList.TryAdd(getItemTuple.Item1.ItemID, itemPredictData);
                                            LogStats.TotalItemsFound++; // Increment total items found for price prediction
                                        }
                                    }
                                }
                            }
                            finally
                            {
                                semaphore.Release();
                            }
                        }));
                    }
                    await Task.WhenAll(tasks);
                }

                var maxBatchStartTime = validSoldItemsDict.Max(item => item.Value.StartTime);
                if (term.MaxCategoryStartDateToSave < maxBatchStartTime)
                    term.MaxCategoryStartDateToSave = maxBatchStartTime;


                var itemsSoldList = validSoldItemsDict.Values.ToList();
                if (itemsSoldList.Any())
                {
                    if (_config.AnalysisType == AnalysisType.SoldItems)
                        await ItemDataToDB.SendItemDataToDB(itemsSoldList, _config);
                }

                if (invalidItemsDict.Any())
                {
                    await ItemDataToDB.SendInvalidItemDataToDb(invalidItemsDict.Values.ToList(), _config);
                }
                // Send to ItemPredict table if using secondary database
                if (itemPredictList.Any())
                {
                    await _dataAnalysisToDb.InsertItems(itemPredictList.Values.ToList());
                }
            }
            finally
            {
                semaphore.Dispose();
            }
        }

        public async Task ProcessBatchFromFile(List<SerpParser.SerpItem> serpItems, string searchName)
        {
            LogStats.TotalItems = serpItems.Count; // Set total items for this batch

            var semaphore = new SemaphoreSlim(_config.ThreadCount);
            var tasks = new List<Task>();
            var validSoldItemsDict = new ConcurrentDictionary<long, ItemDetails>();

            var invalidItemsDict = new ConcurrentDictionary<long, InvalidItem>();
            var itemPredictList = new ConcurrentDictionary<string, ItemPredictData>();
            try
            {
                for (var itemIndex = 0; itemIndex < serpItems.Count; itemIndex++)
                {
                    LogStats.CurrentItemIndex = itemIndex + 1; // Update current item index
                    var serpItem = serpItems[itemIndex];

                    await semaphore.WaitAsync();

                    tasks.Add(Task.Run(async () =>
                    {
                        try
                        {
                            if (itemIndex % 1 == 0)
                            {
                                // Update progress with actual statistics
                                _logger.LogProgress();
                            }

                            if (_config.AnalysisType == AnalysisType.SoldItems)
                            {
                                await ProcessSoldTradingItem(serpItem, validSoldItemsDict, invalidItemsDict);
                            }
                            if (_config.AnalysisType == AnalysisType.DataAnalysis)
                            {
                                // Get the original eBay item data and create ItemPredictData
                                var getItemTuple = await GetItems.GetItemsDetails(_apiContext, serpItem, _config.AnalysisType);
                                if (getItemTuple != null)
                                {
                                    var itemPredictData = CreateItemPredictData(getItemTuple.Item1, getItemTuple.Item2, searchName, serpItem);
                                    if (itemPredictData != null)
                                    {
                                        itemPredictList.TryAdd(getItemTuple.Item1.ItemID, itemPredictData);
                                        LogStats.TotalItemsFound++; // Increment total items found for price prediction
                                    }
                                }
                            }
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }));
                }

                await Task.WhenAll(tasks);

                var itemsSoldList = validSoldItemsDict.Values.ToList();
                if (itemsSoldList.Any())
                {
                    if (_config.AnalysisType == AnalysisType.SoldItems)
                        await ItemDataToDB.SendItemDataToDB(itemsSoldList, _config);
                }

                if (invalidItemsDict.Any())
                {
                    //await ItemDataToDB.SendInvalidItemDataToDb(invalidItemsDict.Values.ToList(), _config);
                }

                // Send to ItemPredict table if using secondary database
                if (itemPredictList.Any())
                {
                    await _dataAnalysisToDb.InsertItems(itemPredictList.Values.ToList());
                }
            }
            finally
            {
                semaphore.Dispose();
            }
        }

        private void ValidateConfiguration()
        {
            if (_config.ThreadCount <= 0)
                throw new InvalidOperationException("ThreadCount must be greater than 0");
            if (_config.DaysBack >= 0)
                throw new InvalidOperationException("DaysBack must be negative");
            if (string.IsNullOrWhiteSpace(_config.ApiToken))
                throw new InvalidOperationException("ApiToken is required");
            if (string.IsNullOrWhiteSpace(_config.DatabaseConfigurationName))
                throw new InvalidOperationException("DatabaseConfigurationName is required");
            if (_config.TradingApiEnabled && (_config.TradingApiDatabaseConfigs == null || _config.TradingApiDatabaseConfigs.Length == 0))
                throw new InvalidOperationException("TradingApiDatabaseConfigs is required when TradingApiEnabled is true");
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _processingLock.Dispose();
                _disposed = true;
            }
        }
    }
}
