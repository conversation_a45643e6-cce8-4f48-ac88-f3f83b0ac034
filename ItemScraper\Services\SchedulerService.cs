using ItemScraper.Configuration;

namespace ItemScraper.Services
{
    public class SchedulerService
    {
        private readonly ScraperConfig _config;
        private readonly LoggingService _logger;

        public SchedulerService(ScraperConfig config, LoggingService logger)
        {
            _config = config;
            _logger = logger;
        }

        public async Task WaitForNextRun()
        {
            DateTime now = DateTime.UtcNow;
            TimeSpan currentTime = now.TimeOfDay;

            bool isWithinRestrictedWindow = IsWithinRestrictedWindow(currentTime);

            if (isWithinRestrictedWindow)
            {
                DateTime next8am = CalculateNext8am(now);
                TimeSpan delay = next8am - now;

                if (delay.TotalMilliseconds < 0)
                {
                    delay = TimeSpan.Zero;
                }

                _logger.LogMessage($"Scraping completed between 3 PM and 8 AM UTC. Waiting until {next8am:s} UTC to start next run.");
                await Task.Delay(delay);
            }
            else
            {
                _logger.LogMessage("Scraping completed between 8 AM and 3 PM UTC. Starting next run immediately.");
            }
        }

        private bool IsWithinRestrictedWindow(TimeSpan currentTime)
        {
            if (_config.RestrictedWindowStart <= _config.RestrictedWindowEnd)
            {
                return currentTime >= _config.RestrictedWindowStart && currentTime <= _config.RestrictedWindowEnd;
            }
            
            return currentTime >= _config.RestrictedWindowStart || currentTime <= _config.RestrictedWindowEnd;
        }

        private DateTime CalculateNext8am(DateTime now)
        {
            DateTime next8am = new DateTime(now.Year, now.Month, now.Day, 8, 0, 0, DateTimeKind.Utc);
            
            if (now.TimeOfDay > _config.RestrictedWindowEnd)
            {
                next8am = next8am.AddDays(1);
            }

            return next8am;
        }
    }
}
