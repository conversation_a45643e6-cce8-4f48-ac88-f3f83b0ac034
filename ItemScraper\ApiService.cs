using System.Diagnostics;
using System.Text.RegularExpressions;
using eBay.Service.Core.Soap;
using ItemScraper.Services;


namespace ItemScraper
{
    public static class ApiService
    {
        public static bool CacheEnabled = false;//Debugger.IsAttached;
        public static async Task<ItemType?> GetItem(bool includeItemSpecifics, eBay.Service.Core.Sdk.ApiContext apiContext, DetailLevelCodeType[] detailLevelList, string itemId)
        {
            try
            {
                var client = new EbayApiClient(new HttpClient());
                string itemStr;
                if (CacheEnabled)
                {

                    const string folder =
                        @"C:\ZOCS\Visual Studio 2010\Projects\_EbayPrograms\ItemScraper\ItemScraper\bin\Debug\net8.0\_cache";
                    var fullPath = Path.Combine(folder, $"{itemId}.txt");

                    if (File.Exists(fullPath))
                        itemStr = await File.ReadAllTextAsync(fullPath);
                    else
                    {
                        itemStr = await client.GetItemAsync(itemId, includeItemSpecifics, apiContext, detailLevelList);
                        await File.WriteAllTextAsync(fullPath, itemStr);
                    }
                }
                else
                {
                    itemStr = await client.GetItemAsync(itemId, includeItemSpecifics, apiContext, detailLevelList);
                }

                // Adjusted pattern to make <ListingDesigner> optional
                const string pattern = "<Item>(.*?)<ListingDesigner>.*?</ListingDesigner>(.*?)</Item>|<Item>(.*?)</Item>";

                // Use RegexOptions.Compiled for better performance.
                var match = Regex.Match(itemStr, pattern, RegexOptions.Singleline | RegexOptions.Compiled);
                if (!match.Success)
                {
                    return null;
                }

                // Combine the matched groups, skipping over ListingDesigner if it's not present and removing <IntangibleItem>
                var itemContent = (match.Groups[1].Success ? match.Groups[1].Value : match.Groups[3].Value).Trim();
                var additionalContent = match.Groups[2].Value.Trim();

                // Remove any occurrences of <IntangibleItem> or its closing tag
                additionalContent = Regex.Replace(additionalContent, "<IntangibleItem>.*?</IntangibleItem>", "", RegexOptions.Singleline);
                additionalContent = Regex.Replace(additionalContent, "<IncludePrefilledItemInformation>.*?</IncludePrefilledItemInformation>", "", RegexOptions.Singleline);
                additionalContent = Regex.Replace(additionalContent, "<PostCheckoutExperienceEnabled>.*?</PostCheckoutExperienceEnabled>", "", RegexOptions.Singleline);
                itemContent = Regex.Replace(itemContent, "<PaymentDetails>.*?</PaymentDetails>", "", RegexOptions.Singleline);
                // Construct the final XML format
                var fixedXml = $"<ItemType>{itemContent}{additionalContent}</ItemType>";

                //fixedXml = File.ReadAllText("item.xml");
                var item = XmlSerializationExtensions.DeserializeXmlWithNamespace<ItemType>(fixedXml, "urn:ebay:apis:eBLBaseComponents");
                if (string.IsNullOrEmpty(item.Title))
                {


                }
                if (item.SellingStatus?.HighBidder != null && fixedXml.Contains("<UserID>") && !fixedXml.Contains("<FeedbackScore>"))
                {
                    item.SellingStatus.HighBidder.FeedbackScore = -1;
                }

                return item;
            }
            catch (Exception ex)
            {
                Console.WriteLine("GetItem:" + ex.Message);
            }
            return new ItemType
            {
                ItemID = itemId
            };

        }
    }
}
