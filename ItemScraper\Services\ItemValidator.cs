using System.Collections.Concurrent;
using eBay.Service.Core.Soap;
using ItemScraper.Configuration;

namespace ItemScraper.Services
{
    public class ItemValidator
    {
        private ConcurrentDictionary<long, byte> _existingBadItems;
        private ConcurrentDictionary<long, byte> _existingItemIds;
        private readonly LoggingService _logger;
        private readonly AnalysisType _analysisType;

        public ItemValidator(LoggingService logger, AnalysisType analysisType)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _analysisType = analysisType;
            _existingBadItems = new ConcurrentDictionary<long, byte>();
            _existingItemIds = new ConcurrentDictionary<long, byte>();
        }

        public async Task FetchExistingItemsCache(ScraperConfig config)
        {
            if (!System.Diagnostics.Debugger.IsAttached && config.AnalysisType == AnalysisType.SoldItems)
            {
                _logger.LogMessage("Fetching Invalid Items Started");
                var badItems = await InvalidItemsToDB.FetchInvalidItemIdsFromDB(DatabaseConfigurations.GetConfiguration(config.DatabaseConfigurationName));
                foreach (var item in badItems)
                {
                    _existingBadItems.TryAdd(item, 0); // Use TryAdd for thread-safety
                }
                _logger.LogMessage("Fetching Invalid Items Complete");

                _logger.LogMessage("Fetching Valid Items Started");
                //_existingItemIds = await ItemDataToDB.FetchItemIdsFromDBInBatches(DateTime.UtcNow.AddDays(-2), config, 1000000);
                _logger.LogMessage($"Fetching Valid Items Complete. Got {_existingItemIds.Count}");
            }
        }

        public IEnumerable<SerpParser.SerpItem> KeepUnseenItems(IEnumerable<SerpParser.SerpItem> skippedToSearchOldItems)
        {
            var unseen = skippedToSearchOldItems.Where(item =>
                !_existingItemIds.ContainsKey(item.ItemID) &&
                !_existingBadItems.ContainsKey(item.ItemID));
            return unseen;
        }

        public void AddInvalidItem(long itemId)
        {
            // TryAdd returns true if the key was added, false if it already existed.
            // This preserves the original logic of only executing the inner block if the item was newly added.
            if (_existingBadItems.TryAdd(itemId, 0))
            {
                //try
                //{
                //    await InvalidItemsToDB.SendInvalidItemDataToDB(new List<InvalidItem>
                //    {
                //        new InvalidItem
                //        {
                //            FetchTime = DateTime.UtcNow,
                //            ItemId = itemId
                //        }
                //    });
                //}
                //catch (Exception ex)
                //{
                //    _logger.LogError($"Error adding invalid item {itemId}", ex);
                //}
            }
        }

        public void AddValidItem(long itemId)
        {
            _existingItemIds.TryAdd(itemId, 0); // Use TryAdd for thread-safety
        }

        public bool ValidateItemDetails(ItemType item)
        {
            if (_analysisType == AnalysisType.SoldItems)
            {
                if (item.ListingDetails is { EndingReasonSpecified: true })
                {
                    var endingReason = item.ListingDetails.EndingReason.ToString();
                    return endingReason == "SellToHighBidder" || endingReason == "Sold";
                }
            }
            return true;
        }
    }
}
