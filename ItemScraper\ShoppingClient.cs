using NetJSON;
using System.Diagnostics;
using uBuyFirst.API.ShoppingAPI;

namespace ItemScraper
{
    public enum Result
    {
        Success,
        Limited,
        Error
    }

    public class GetMultipleItemsCallResult
    {
        public ShoppingAPIJson.GetMultipleItemsResponseType Response { get; set; }
        public Result Result { get; set; }
    }

    internal class ShoppingClient
    {
        private static readonly NetJSONSettings s_netJsonSettings = new() { TimeZoneFormat = NetJSONTimeZoneFormat.Utc };
        private static readonly HttpClient HttpClient = new();
        public static async Task<GetMultipleItemsCallResult> SimpleItemCallAsync(List<long> idList)
        {
            try
            {
                var jsonText = await GetMultipleItemsString(idList);
                if (jsonText != null && jsonText.Contains("Applications from this IP address"))
                {

                    return new GetMultipleItemsCallResult
                    {
                        Response = new ShoppingAPIJson.GetMultipleItemsResponseType { Item = [] },
                        Result = Result.Limited
                    };
                }

                var response = NetJSON.NetJSON.Deserialize<ShoppingAPIJson.GetMultipleItemsResponseType>(jsonText, s_netJsonSettings);

                if (response == null)
                {
                    return new GetMultipleItemsCallResult
                    {
                        Response = new ShoppingAPIJson.GetMultipleItemsResponseType { Item = [] },
                        Result = Result.Error
                    };
                }

                if (response.Item == null)
                {
                    response.Item = [];
                }
                else
                {

                }

                return new GetMultipleItemsCallResult { Response = response, Result = Result.Success };

            }
            catch (Exception ex)
            {
                Debug.WriteLine("Download 20:" + string.Join(",", idList) + ex.Message);
            }

            return new GetMultipleItemsCallResult { Response = new ShoppingAPIJson.GetMultipleItemsResponseType { Item = [] }, Result = Result.Error };
        }
        public static async Task<string> GetMultipleItemsString(List<long> idList)
        {
            var apiKey = "eBayInc52-907e-4b8a-ba0c-707469bb4d5";

            apiKey = $"&appid={apiKey}";

            var idListString = string.Join(",", idList);
            const string BaseAddress = "https://open.api.ebay.com/shopping";
            var urlQuery = $"?ItemID={idListString}{apiKey}&callname=GetMultipleItems&responseencoding=JSON&siteid=0&version=1199&IncludeSelector=Details";

            var jsonText = await HttpClient.GetStringAsync(BaseAddress + urlQuery);
            return jsonText;
        }
        public static ItemDetails CreateSoldItemDetails(ShoppingAPIJson.SimpleItemType item)
        {
            if (item?.PrimaryCategoryID == null) return null;

            var itemId = long.Parse(item.ItemID);
            var categoryId = int.Parse(item.PrimaryCategoryID);
            var startTime = item.StartTime;
            var endTime = item.EndTime;
            var price = (decimal)item.ConvertedCurrentPrice.Value;
            var soldCount = item.QuantitySold;
            var duration = (long)Math.Round((endTime - startTime).TotalSeconds);

            string buyerName = "^^";
            int buyerFeedbackCount = 0;
            bool buyerFeedbackPrivate = false;

            if (item is { HighBidder: not null })
            {
                buyerName = item.HighBidder.UserID.Replace("***", "");
                if (buyerName.Length > 40)
                {
                    buyerName = buyerName.Substring(0, 40);
                }

                if (item.HighBidder.FeedbackScore == null && !string.IsNullOrEmpty(buyerName))
                {
                    buyerFeedbackCount = 0;
                    buyerFeedbackPrivate = true;
                }
                else if (item.HighBidder.FeedbackScore != null)
                {
                    buyerFeedbackCount = item.HighBidder.FeedbackScore < 0 ? 0 : item.HighBidder.FeedbackScore;
                }
            }

            string sellerName = item.Seller.UserID.Replace("***", "");
            if (sellerName.Length > 40)
            {
                sellerName = sellerName.Substring(0, 40);
            }

            int? sellerFeedbackCount = item.Seller.FeedbackScore;
            if (sellerName != "" && (sellerFeedbackCount == null || sellerFeedbackCount < 0))
            {
                sellerFeedbackCount = 0;
            }

            var listingStatus = 0;
            switch (item.ListingStatus)
            {
                case "Active":
                    listingStatus = 0;
                    break;

                case "Completed":
                    if (item.QuantitySold > 0)
                    {
                        listingStatus = 2;
                    }
                    else
                    {

                    }
                    break;

                default:
                    listingStatus = 0;
                    break;
            }
            return new ItemDetails
            {
                item_id = itemId,
                LeafCategoryID = categoryId,
                StartTime = startTime,
                EndTime = endTime,
                FetchTime = DateTime.UtcNow,
                Price = price,
                QuantitySold = soldCount,
                Duration = duration,
                BuyerName = buyerName,
                BuyerFeedbackCount = buyerFeedbackCount,
                BuyerFeedbackPrivate = buyerFeedbackPrivate,
                SellerName = sellerName,
                SellerFeedbackCount = sellerFeedbackCount,
                ListingStatus = listingStatus
            };
        }
    }
}
