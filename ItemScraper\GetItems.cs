﻿using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using ItemScraper.Services;

namespace ItemScraper
{
    public static class GetItems
    {
        public static int ItemsDownloaded;

        public static async Task<Tuple<ItemType, DateTime>> GetItemsDetails(ApiContext apiContext, SerpParser.SerpItem idTime, AnalysisType analysisType)
        {
            var includeItemSpecifics = false;

            DetailLevelCodeType[] detailLevels;
            if (analysisType == AnalysisType.SoldItems)
                detailLevels = new[] { DetailLevelCodeType.ItemReturnCategories };
            else
            {
                detailLevels = new[] { DetailLevelCodeType.ReturnAll };
                includeItemSpecifics = true;
            }

            var item = await ApiService.GetItem(includeItemSpecifics, apiContext, detailLevels, idTime.ItemID.ToString());
            if(item == null) 
                return null;
            return Tuple.Create(item, idTime.Date);
        }
    }
}
