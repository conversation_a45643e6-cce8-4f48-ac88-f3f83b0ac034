using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;

namespace ItemScraper
{
    public static class EbayApi
    {
        public static ApiContext GetApiContext(string apiToken)
        {
            string[] keys;

            {
                keys = new[] {
                    "7e24c9e0-744b-4bda-9289-83d1d02b3a4c",
                    "OCPCee566-3f54-4549-80e6-2d8bd40d28b",
                    "1b902f48-e1c0-4181-9e27-a201c3f09a30",
                    "OCPC-OCPC910dc-1b60--ikeud",
                    "https://api.ebay.com/wsapi",
                    "https://signin." + "ebay.com" + "/ws/eBayISAPI.dll?SignIn",
                    "ebay.com"
                };
            }
            var _ebayDomain = keys[6];
            var apiContext = new ApiContext
            {
                SoapApiServerUrl = keys[4], SignInUrl = keys[5], ApiCredential = new ApiCredential(), RuName = keys[3]
                //WebProxy = new WebProxy("http://127.0.0.1:8888"),
            };
            apiContext.ApiCredential.ApiAccount = new ApiAccount {Application = keys[1], Developer = keys[0], Certificate = keys[2]};
            apiContext.Site = SiteCodeType.US;
            apiContext.ApiCredential.eBayToken = apiToken;
            //prospector
            //apiContext.ApiCredential.eBayToken = "AgAAAA**AQAAAA**aAAAAA**hUTcWw**nY+sHZ2PrBmdj6wVnY+sEZ2PrA2dj6wNlYGmDZOKoQudj6x9nY+seQ**PEgEAA**AAMAAA**Q5h0aNLL2AT6a2ty/CMhlDb07DH81rHBTXTWbuMBI1BWhz8fCkgNZrM6QU5XZvJ2L9teHtTm2WbyLuK3fWDu0eGFUO9mGUybvIRWWjEcS/tmLO+IFWn5QGIZ/8UQFrx6uK90eBOc4syzwfrsOf6tv8zxdbpUoFnC64cguFjELxO4T3/jseqTIyt0PVjwi03adV0MXI+j8U8GH0jcpUaUbISzulJuVqmduxdfSZHoISRa9FHnA6ibtP7b9M482oPZLePsDDfwOK/Fs41EIqxHMOufu9+Km2vvFwGdDjLhVRbh6hd2Vz1JC517gvtLIpZAkq9zhzYAn2rMSl3vzMviXEv7TeLdf04WP6hXJMNzHSmfUAef6mb7uYcpbNCQsotKfL5SAYO51iqFNomKxBPhCApciF4n3kuQC03ApjbsYF9AtL48G2z2Gl66KDwDwKfU8vtBZUS89jUccE8M8wxl2wyjSpuAMtFZwnUC3Eh7YlI4IgnJUf7j6hmPR2ucb2DjWlc+xtcTy+xnpdU3DTiR3Qb/Av/FHJYSH3ZC89az27U1u+mKNdrfncMfTarj4m/DD9t1EF73FSoaSeEEJE0Yljlo+Ad88PDnEPKMa3ZyhnG6QWumvQk66Si41uE0Li80rBIC8k9YYtTd0lynFmfFhYqxmRLUNpJZsgmk5/Oh4FFaFpnA3fO7oGOpp1xSUHvOEgjiSYLugewWhsbFgq2ad8A1zXDZRxeuwkaigCKReUsWt0Ez6NjYdoHccrq1Nm9z";
            //ubf
            //apicontext.ApiCredential.eBayToken = "AgAAAA**AQAAAA**aAAAAA**hYjMVQ**nY+sHZ2PrBmdj6wVnY+sEZ2PrA2dj6wNlYGmDZOKoQudj6x9nY+seQ**I+kCAA**AAMAAA**4QpYHf17kY89jM0yG+1KOB9tzDWSUOO9PzyhQ2xBcbecYTIhkyjEbVCz4NeC4oPv7U+DcgpW9hzy2QG7PnNe3eeJBaMvOvnxR40CVfV9LdVJtYB3CMv505s6JKDbFxeYhOcLJarQycoZkUWkmxYifbIxivopGEUmtALVBRQzD3FZRN3XJPwqkIuxET8qdlnWfiBhBj6KRcLqxpiVo+aFlX2zFNpnVbyKqgjPF1ugcW2Dx1hAxMWEijWfWoRDRGVE/zlERfCH7ZT0v6IGZ96LkfFS/lkVuq5w3CIFoKWlG/CixDmdM4qlbxWPF66VdT1YtHH9Ru4CE6PywZ4KRhadjCiHezr/Mx9J2TkvaaTmRMvyZm4h/aSP2D98s39ZWJQqjgsk7PMC+ndrYxsF97Qm31NdI1tqpRtUNtOrisyu0ixujmfQWuV6lBGyXTiDY5HfwbiotPWgk0VpuInl2KGX5LHAyTb4apKtrMBry8xdJXYk61w75MMOotDlnsaLFpRrdm3UBHSPjE7OlDUbk+PK69cXETQavoms6Hu0M7iSMEpqt497d2mVrUJLFqhCYBL9RF7Y2jiBn97AAf9cpB64CrgnSa3R2oh9FvU15May4yZqr1mw3b8w6osomxb9ETxT/CaMHVrct2p0VsncN0acB1emw8OueWeikuZ+KTqs9UZbolTg44v3jY+2tf3lHeJiVJ9x/km2lSXQM4OJBKgJKeUJ+Pcswak7dLnT0Xl+aSu3jZaHNAYKpvfMIOLzkCkq";
            //limited
            //apicontext.ApiCredential.eBayToken = "AgAAAA**AQAAAA**aAAAAA**/VM7Vw**nY+sHZ2PrBmdj6wVnY+sEZ2PrA2dj6wNlYGmDZOKoQudj6x9nY+seQ**O0oDAA**AAMAAA**ZkIgpfvfHBFRyMYa+HJ5VjiKvVUUW7apaXmKPbrKPEbvZ4+3/hv7DKGAbtURGKkZPoOrgDCTCco4HN+2KcqehxfZqyVV4ZjtuWMuBeXr9uJBM0qy5Pz+XURCFUWw7/5GpzFIpc/dVBLpdkBQpQ15p6vXjVodeY+RYGWMQ1MTRJZbA6SWazc0eRgMtoT+K1l29EY7a0OlshCzKe2MY4hNVBABLELI0CbUzYwhEp4nNXzAGTnV+DMZ7ULeZjDq1Vtmuk0Lo+56eSsrALoPkZyG7R3H9yIbFPzsXEZqse3E5vIqfEWSf1ykkokDFwrXPFWM4o7AH2z7zJdzf17qS1zqx7I5Fw0UhwW3UPdIPA3WlLPBVD93TZp2VQLgfp/aXfB5eHepqNbxMMpl/7TuE0WjVwRDsSGp2uxhaKJMndAjVtpSeEmDXUII7HLX0g3DfyYoDpmMoVxkZC8jzwM3X1EiodAgWTJ0JfrfMh1RdqLXbz+whYBYhMISGW42P0lsTaHCqNfYabPEyDJEf2ONzyD2Dyu7TAVOL8DOfuCjd92dGlB2B5OYQTltskLAuDG1CcQC5PfxpMMynbtIN9GP91jq7lGOCKQNfWWN58y0vgjs6uqFSwqaeSNUmKUxoLVrexV3YwcG9y0mleLTAapXoQAQWJP0SiZefz5Ps4VT9iVROYOMBSyWNS3jSW6w+ZM7LgGYPI/TryhbVIzjWesShGX+l4g2/Ffo2LvNP883AXI7y3M9IVMlSWTtmGyP5z2ZydaK";

            return apiContext;
        }
    }
}