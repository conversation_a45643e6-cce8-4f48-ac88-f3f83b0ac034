using System.Text.RegularExpressions;
using System.Web;

namespace ItemScraper.Services
{
    public static class TextProcessor
    {
        public static string StripHTML(string HTMLText, bool decode = true)
        {
            Regex reg = new Regex("<[^>]+>", RegexOptions.IgnoreCase);
            var stripped = reg.Replace(HTMLText, " ");
            var htmlDecoded = decode ? HttpUtility.HtmlDecode(stripped) : stripped;
            string removedEmptyLines = string.Join("\n", htmlDecoded
                .Split(new[] { '\n' }, StringSplitOptions.None)
                .Where(line => !string.IsNullOrWhiteSpace(line)));
            return removedEmptyLines;
        }
    }
}
