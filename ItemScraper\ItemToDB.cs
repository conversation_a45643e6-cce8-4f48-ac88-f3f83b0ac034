using System.Collections.Concurrent;
using Npgsql;
using System.Diagnostics;
using ItemScraper.Configuration;

namespace ItemScraper;

internal class ItemDataToDB
{
    private const string TempTableName = "temp_sold_data";
    private const int DefaultBatchSize = 5000;

    public static DatabaseConfiguration GetCurrentDbConfig(ScraperConfig config)
    {
        return DatabaseConfigurations.GetConfiguration(config.DatabaseConfigurationName);
    }

    public static async Task<HashSet<long>> FetchItemIdsFromDBInBatches(DateTime dateTime, ScraperConfig config,
         int batchSize = 1000000)
    {
        var dbConfig = GetCurrentDbConfig(config);
        var stopwatch = Stopwatch.StartNew();
        var itemIds = new HashSet<long>();

        await using var connection = new NpgsqlConnection(dbConfig.GetConnectionString());

        try
        {
            await connection.OpenAsync();

            var fetchItemIdsSql = $@"
            SELECT item_id 
            FROM {dbConfig.SchemaName}.{dbConfig.TableName}
            WHERE (listing_status = 2 AND end_time > @EndTime) 
               OR (listing_status = 0)";

            if (!string.IsNullOrEmpty(config.CategoryId))
            {
                fetchItemIdsSql += " AND leaf_category_id = @CategoryID";
            }

            await using var command = new NpgsqlCommand(fetchItemIdsSql, connection);
            command.Parameters.AddWithValue("@EndTime", dateTime);

            if (!string.IsNullOrEmpty(config.CategoryId))
            {
                command.Parameters.AddWithValue("@CategoryID", config.CategoryId);
            }

            await using var reader = await command.ExecuteReaderAsync();
            var buffer = new List<long>(batchSize);

            while (await reader.ReadAsync())
            {
                buffer.Add(reader.GetInt64(0));

                if (buffer.Count >= batchSize)
                {
                    foreach (var id in buffer)
                    {
                        itemIds.Add(id);
                    }
                    buffer.Clear();
                    Console.WriteLine(itemIds.Count);
                }
            }

            foreach (var id in buffer)
            {
                itemIds.Add(id);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"Error fetching item IDs: {e.Message}");
            throw;
        }

        stopwatch.Stop();
        Console.WriteLine($"Fetched {itemIds.Count} item IDs in {stopwatch.Elapsed.TotalSeconds} seconds");

        return itemIds;
    }

    public static async Task SendItemDataToDB(List<ItemDetails> itemDetailsList, ScraperConfig config)
    {
        if (!itemDetailsList.Any()) return;

        var dbConfig = GetCurrentDbConfig(config);
        var stopwatch = Stopwatch.StartNew();

        await using var connection = new NpgsqlConnection(dbConfig.GetConnectionString());
        await connection.OpenAsync();
        await using var transaction = await connection.BeginTransactionAsync();

        try
        {
            // Create temporary table based on configured columns
            var createTempTableSql = $@"
            CREATE UNLOGGED TABLE {TempTableName} (
                LIKE {dbConfig.SchemaName}.{dbConfig.TableName} INCLUDING ALL
            ) WITH (OIDS=FALSE)";

            await using var createTempTableCommand = new NpgsqlCommand(createTempTableSql, connection, transaction);
            await createTempTableCommand.ExecuteNonQueryAsync();

            foreach (var batch in itemDetailsList.Chunk(DefaultBatchSize))
            {
                var columnList = string.Join(", ", dbConfig.Columns);

                await using var importer = await connection.BeginBinaryImportAsync(
                    $"COPY {TempTableName} ({columnList}) FROM STDIN (FORMAT BINARY)");

                foreach (var item in batch)
                {
                    await importer.StartRowAsync();

                    // Write only configured columns
                    foreach (var column in dbConfig.Columns)
                    {
                        switch (column)
                        {
                            case "item_id":
                                await importer.WriteAsync(item.item_id, NpgsqlTypes.NpgsqlDbType.Bigint);
                                break;
                            case "leaf_category_id":
                                await importer.WriteAsync(item.LeafCategoryID, NpgsqlTypes.NpgsqlDbType.Integer);
                                break;
                            case "start_time":
                                await importer.WriteAsync(DateTime.SpecifyKind(item.StartTime, DateTimeKind.Unspecified), NpgsqlTypes.NpgsqlDbType.Timestamp);
                                break;
                            case "end_time":
                                await importer.WriteAsync(DateTime.SpecifyKind(item.EndTime, DateTimeKind.Unspecified), NpgsqlTypes.NpgsqlDbType.Timestamp);
                                break;
                            case "fetch_time":
                                await importer.WriteAsync(DateTime.SpecifyKind(item.FetchTime, DateTimeKind.Unspecified), NpgsqlTypes.NpgsqlDbType.Timestamp);
                                break;
                            case "price":
                                await importer.WriteAsync(item.Price, NpgsqlTypes.NpgsqlDbType.Numeric);
                                break;
                            case "quantity_sold":
                                await importer.WriteAsync(item.QuantitySold, NpgsqlTypes.NpgsqlDbType.Integer);
                                break;
                            case "duration":
                                await importer.WriteAsync(item.Duration, NpgsqlTypes.NpgsqlDbType.Integer);
                                break;
                            case "listing_status":
                                await importer.WriteAsync(item.ListingStatus, NpgsqlTypes.NpgsqlDbType.Smallint);
                                break;
                            case "buyer_name":
                                await importer.WriteAsync(item.BuyerName, NpgsqlTypes.NpgsqlDbType.Varchar);
                                break;
                            case "buyer_feedback_count":
                                await importer.WriteAsync(item.BuyerFeedbackCount ?? (object)DBNull.Value, NpgsqlTypes.NpgsqlDbType.Integer);
                                break;
                            case "seller_name":
                                await importer.WriteAsync(item.SellerName, NpgsqlTypes.NpgsqlDbType.Varchar);
                                break;
                            case "seller_feedback_count":
                                await importer.WriteAsync(item.SellerFeedbackCount ?? (object)DBNull.Value, NpgsqlTypes.NpgsqlDbType.Integer);
                                break;
                            case "buyer_feedback_private":
                                await importer.WriteAsync(item.BuyerFeedbackPrivate, NpgsqlTypes.NpgsqlDbType.Boolean);
                                break;
                        }
                    }
                }

                await importer.CompleteAsync();
            }

            var mergeSql = $@"
            INSERT INTO {dbConfig.SchemaName}.{dbConfig.TableName}
            SELECT * FROM {TempTableName}
            ON CONFLICT (item_id) 
            DO UPDATE SET
                {string.Join(",\n                ", dbConfig.Columns.Where(col => col != "item_id").Select(col => $"{col} = EXCLUDED.{col}"))}";

            await using var mergeCommand = new NpgsqlCommand(mergeSql, connection, transaction);
            var rowsAffected = await mergeCommand.ExecuteNonQueryAsync();

            await using var dropTempTableCommand = new NpgsqlCommand(
                $"DROP TABLE IF EXISTS {TempTableName}", connection, transaction);
            await dropTempTableCommand.ExecuteNonQueryAsync();

            await transaction.CommitAsync();

            stopwatch.Stop();
            Console.WriteLine($"{DateTime.UtcNow:s} Inserted {rowsAffected} rows in {stopwatch.Elapsed.TotalSeconds} seconds");
        }
        catch (Exception e)
        {
            await transaction.RollbackAsync();
            Console.WriteLine($"Error sending item data to DB: {e.Message}");
            throw;
        }
    }
    public static async Task SendInvalidItemDataToDb(List<InvalidItem> invalidItemDetailsList, ScraperConfig config)
    {
        var dbConfig = GetCurrentDbConfig(config);
        await using var connection = new NpgsqlConnection(dbConfig.GetConnectionString());


        await connection.OpenAsync();
        string insertSql = $@"
                INSERT INTO {dbConfig.SchemaName}.invalid_items (item_id, fetch_time)
                VALUES (@ItemId, @FetchTime)
                ON CONFLICT (item_id) DO 
                UPDATE SET fetch_time = EXCLUDED.fetch_time;";

        await using NpgsqlCommand command = new NpgsqlCommand(insertSql, connection);
        foreach (var itemDetails in invalidItemDetailsList)
        {
            command.Parameters.Clear(); // Clear parameters for each item
            command.Parameters.AddWithValue("@ItemId", itemDetails.ItemId);
            command.Parameters.AddWithValue("@FetchTime", (object)itemDetails.FetchTime ?? DBNull.Value);

            await command.ExecuteNonQueryAsync();
        }
    }
}
