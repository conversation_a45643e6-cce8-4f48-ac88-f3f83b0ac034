﻿using ItemScraper.Configuration;
using Npgsql;

namespace ItemScraper;

public class InvalidItem
{
    public long ItemId { get; set; } // Matches item_id in invalid_items table
    public DateTime? FetchTime { get; set; } // Nullable, matches fetch_time
}

internal class InvalidItemsToDB
{
    //static string connectionString = @"Server=ubf.dynu.net;Port=5432;Database=item_speed_db;User Id=postgres;Password=********************************************;";

    public static async Task<HashSet<long>> FetchInvalidItemIdsFromDB(DatabaseConfiguration configuration)
    {
        HashSet<long> itemIds = new HashSet<long>();

        using (NpgsqlConnection connection = new NpgsqlConnection(configuration.GetConnectionString()))
        {
            await connection.OpenAsync();
            string fetchItemIdsSql = @"SELECT item_id FROM invalid_items WHERE fetch_time >= NOW() - INTERVAL '14 days'";

            using (NpgsqlCommand command = new NpgsqlCommand(fetchItemIdsSql, connection))
            {
                using (NpgsqlDataReader reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        long itemId = reader.GetInt64(0); // Assuming item_id is stored as bigint
                        itemIds.Add(itemId);
                    }
                }
            }
        }

        return itemIds;
    }
}