using eBay.Service.Core.Soap;

namespace ItemScraper.Services
{
    public class ItemDetailsFactory
    {
        private readonly AnalysisType _analysisType;

        public ItemDetailsFactory(AnalysisType analysisType)
        {
            _analysisType = analysisType;
        }



        public ItemDetails CreateSoldItemDetails(ItemType item)
        {
            if (item?.PrimaryCategory?.CategoryID == null) return null;

            var itemId = long.Parse(item.ItemID);
            var categoryId = int.Parse(item.PrimaryCategory.CategoryID);
            var startTime = item.ListingDetails.StartTime;
            var endTime = item.ListingDetails.EndTime;
            var price = (decimal)item.SellingStatus.ConvertedCurrentPrice.Value;
            var soldCount = item.SellingStatus.QuantitySold;
            var duration = (long)Math.Round((endTime - startTime).TotalSeconds);

            string buyerName = "";
            int buyerFeedbackCount = 0;
            bool buyerFeedbackPrivate = false;

            if (item.SellingStatus.HighBidder != null)
            {
                buyerName = item.SellingStatus.HighBidder.UserID.Replace("***", "");
                if (buyerName.Length > 40)
                {
                    buyerName = buyerName.Substring(0, 40);
                }

                if (item.SellingStatus.HighBidder.FeedbackScore == null && !string.IsNullOrEmpty(buyerName))
                {
                    buyerFeedbackCount = 0;
                    buyerFeedbackPrivate = true;
                }
                else if (item.SellingStatus.HighBidder.FeedbackScore != null)
                {
                    if (item.SellingStatus.HighBidder.FeedbackScore >= 0)
                        buyerFeedbackCount = item.SellingStatus.HighBidder.FeedbackScore;
                }
            }

            string sellerName = item.Seller.UserID.Replace("***", "");
            if (sellerName.Length > 40)
            {
                sellerName = sellerName.Substring(0, 40);
            }

            int? sellerFeedbackCount = item.Seller.FeedbackScore;
            if (sellerName != "" && (sellerFeedbackCount == null || sellerFeedbackCount < 0))
            {
                sellerFeedbackCount = 0;
            }

            return new ItemDetails
            {
                item_id = itemId,
                LeafCategoryID = categoryId,
                StartTime = startTime,
                EndTime = endTime,
                FetchTime = DateTime.UtcNow,
                Price = price,
                QuantitySold = soldCount,
                Duration = duration,
                BuyerName = buyerName,
                BuyerFeedbackCount = buyerFeedbackCount,
                BuyerFeedbackPrivate = buyerFeedbackPrivate,
                SellerName = sellerName,
                SellerFeedbackCount = sellerFeedbackCount,
                ListingStatus = (int)item.SellingStatus.ListingStatus
            };
        }



        public static decimal GetItemPrice(ItemType item)
        {
            switch (item.ListingType)
            {
                case ListingTypeCodeType.FixedPriceItem:
                case ListingTypeCodeType.StoresFixedPrice:
                    return (decimal)item.ListingDetails.ConvertedStartPrice.Value;

                case ListingTypeCodeType.Auction:
                case ListingTypeCodeType.Chinese:
                    if (item.SellingStatus.SoldAsBinSpecified && item.SellingStatus.SoldAsBin)
                        return (decimal)item.ListingDetails.ConvertedBuyItNowPrice.Value;
                    return (decimal)item.SellingStatus.ConvertedCurrentPrice.Value;

                case ListingTypeCodeType.PersonalOffer:
                case ListingTypeCodeType.LeadGeneration:
                    break;
            }
            return 0;
        }
    }
}
