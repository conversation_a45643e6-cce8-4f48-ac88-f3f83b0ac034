using ItemScraper.Services;

namespace ItemScraper.Configuration
{
    public class ScraperConfig
    {
        public string ApiToken { get; set; }
        public int ThreadCount { get; set; }
        public int DaysBack { get; set; }
        public string CategoryId { get; set; }
        public string SearchTermsFile { get; set; }
        public TimeSpan RestrictedWindowStart { get; set; }
        public TimeSpan RestrictedWindowEnd { get; set; }
        public string DatabaseConfigurationName { get; set; }
        public bool TradingApiEnabled { get; set; }
        public string[] TradingApiDatabaseConfigs { get; set; }
        public AnalysisType AnalysisType { get; set; } // New property for analysis type
        public static string DataAnalysisTableName { get; set; }

        public static ScraperConfig data_analysis => new ScraperConfig
        {
            ApiToken = "v^1.1#i^1#f^0#p^3#I^3#r^1#t^Ul4xMF83OkMzNzhGMkI3NkFFRjI5MTJGRjhDRTM0RjZBRERGMDlGXzFfMSNFXjI2MA==",
            ThreadCount = System.Diagnostics.Debugger.IsAttached ? 10 : 10,
            DaysBack = -90,
            CategoryId = "",
            SearchTermsFile = "SearchTerms.analysis.csv",
            RestrictedWindowStart = new TimeSpan(15, 0, 0), // 3 PM UTC
            RestrictedWindowEnd = new TimeSpan(8, 0, 0),    // 8 AM UTC
            DatabaseConfigurationName = "data_analysis",           // Default to primary database
            TradingApiEnabled = false,                      // Disabled by default for backward compatibility
            TradingApiDatabaseConfigs = ["TradingApi"], // Default to TradingApi config
            AnalysisType = AnalysisType.DataAnalysis          // Default to SoldItems analysis
        };
        public static ScraperConfig SoldItems => new ScraperConfig
        {
            ApiToken = "v^1.1#i^1#f^0#p^3#I^3#r^1#t^Ul4xMF83OkMzNzhGMkI3NkFFRjI5MTJGRjhDRTM0RjZBRERGMDlGXzFfMSNFXjI2MA==",
            ThreadCount = System.Diagnostics.Debugger.IsAttached ? 15 : 15,
            DaysBack = -6,
            CategoryId = "",
            SearchTermsFile = "SearchTerms.sold.csv",
            RestrictedWindowStart = new TimeSpan(15, 0, 0), // 3 PM UTC
            RestrictedWindowEnd = new TimeSpan(8, 0, 0),    // 8 AM UTC
            DatabaseConfigurationName = "SoldItems",           // Default to primary database
            TradingApiEnabled = false,                      // Disabled by default for backward compatibility
            TradingApiDatabaseConfigs = ["TradingApi"], // Default to TradingApi config
            AnalysisType = AnalysisType.SoldItems          // Default to SoldItems analysis
        };
        public static ScraperConfig SoldItemsMotors => new ScraperConfig
        {
            ApiToken = "v^1.1#i^1#f^0#p^3#I^3#r^1#t^Ul4xMF83OkMzNzhGMkI3NkFFRjI5MTJGRjhDRTM0RjZBRERGMDlGXzFfMSNFXjI2MA==",
            ThreadCount = System.Diagnostics.Debugger.IsAttached ? 7 : 7,
            DaysBack = -14,
            CategoryId = "",
            SearchTermsFile = "SearchTerms.sold.motors.csv",
            RestrictedWindowStart = new TimeSpan(15, 0, 0), // 3 PM UTC
            RestrictedWindowEnd = new TimeSpan(8, 0, 0),    // 8 AM UTC
            DatabaseConfigurationName = "SoldItemsMotors",           // Default to primary database
            TradingApiEnabled = false,                      // Disabled by default for backward compatibility
            TradingApiDatabaseConfigs = ["TradingApi"], // Default to TradingApi config
            AnalysisType = AnalysisType.SoldItems          // Default to SoldItems analysis
        };
    }
}
