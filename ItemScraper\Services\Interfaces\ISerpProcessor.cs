using ItemScraper.Terms;

namespace ItemScraper.Services.Interfaces
{
    /// <summary>
    /// Handles processing of eBay SERP (Search Engine Results Page) data
    /// </summary>
    public interface ISerpProcessor : IDisposable
    {
        /// <summary>
        /// Gets the total number of SERP URLs downloaded
        /// </summary>
        int SerpUrlsDownloaded { get; }

        /// <summary>
        /// Gets the total number of items in download list
        /// </summary>
        int ItemDownloadListTotal { get; }

        /// <summary>
        /// Processes a single SERP URL and extracts item information
        /// </summary>
        /// <param name="serpUrl">The SERP URL to process</param>
        /// <param name="term">The search term being processed</param>
        /// <param name="serpUrlsLists">List of SERP URL lists to be updated</param>
        /// <returns>True if there are more pages to process, false otherwise</returns>
        Task<bool> ProcessSerpUrl(string serpUrl, SearchTerm term, List<List<string>> serpUrlsLists);

        /// <summary>
        /// Handles splitting of price ranges when results exceed eBay's maximum limit
        /// </summary>
        Task HandlePriceRangeSplit(string htmlContent, string basicSerpUrl, List<List<string>> serpUrlsLists);

        /// <summary>
        /// Handles splitting of date ranges when results exceed eBay's maximum limit
        /// </summary>
        Task HandleDateRangeSplit(string serpUrl, string basicSerpUrl, List<List<string>> serpUrlsLists);

        /// <summary>
        /// Processes a list of SERP items
        /// </summary>
        Task<bool> ProcessSerpItems(List<SerpParser.SerpItem> serpItems, SearchTerm term);
    }
}
