namespace ItemScraper.Configuration
{
    public class DatabaseConfiguration
    {
        public string Name { get; set; }
        public string Host { get; set; }
        public string Port { get; set; }
        public string DatabaseName { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public string SchemaName { get; set; }
        public string TableName { get; set; }
        public List<string> Columns { get; set; }

        public string GetConnectionString()
        {
            return $"Server={Host};Port={Port};Database={DatabaseName};User Id={Username};Password={Password};Include Error Detail=true;";
        }
    }

    public static class DatabaseConfigurations
    {
        private static readonly Dictionary<string, DatabaseConfiguration> Configurations = new()
        {
            {
                "SoldItems", new DatabaseConfiguration
                {
                    Name = "SoldItems",
                    Host = "ubuyfirst-item-data.cluster-ckjfvcdemxab.us-west-2.rds.amazonaws.com",
                    Port = "5432",
                    DatabaseName = "postgres",
                    Username = "postgres",
                    Password = "NCHJu5OO6T79kVuJ0UOzu1LunAhu5r40P2jzkw5LfH7ft6aBFU",
                    SchemaName = "public",
                    TableName = "sold_items",
                    Columns = new List<string>
                    {
                        "item_id", "leaf_category_id", "start_time", "end_time", "fetch_time",
                        "price", "quantity_sold", "duration", "listing_status", "buyer_name",
                        "buyer_feedback_count", "seller_name", "seller_feedback_count",
                        "buyer_feedback_private"
                    }
                }
            },{
                "SoldItemsMotors", new DatabaseConfiguration
                {
                    Name = "SoldItemsMotors",
                    Host = "ubuyfirst-item-data.cluster-ckjfvcdemxab.us-west-2.rds.amazonaws.com",
                    Port = "5432",
                    DatabaseName = "postgres",
                    Username = "postgres",
                    Password = "NCHJu5OO6T79kVuJ0UOzu1LunAhu5r40P2jzkw5LfH7ft6aBFU",
                    SchemaName = "public",
                    TableName = "sold_items_motors",
                    Columns = new List<string>
                    {
                        "item_id", "leaf_category_id", "start_time", "end_time", "fetch_time",
                        "price", "quantity_sold", "duration", "listing_status", "buyer_name",
                        "buyer_feedback_count", "seller_name", "seller_feedback_count",
                        "buyer_feedback_private"
                    }
                }
            },
            {
                "data_analysis", new DatabaseConfiguration
                {
                    Name = "data_analysis",
                    Host = "ubuyfirst-item-data.cluster-ckjfvcdemxab.us-west-2.rds.amazonaws.com",
                    Port = "5432",
                    DatabaseName = "postgres",
                    Username = "postgres",
                    Password = "NCHJu5OO6T79kVuJ0UOzu1LunAhu5r40P2jzkw5LfH7ft6aBFU",
                    SchemaName = "data_analysis",
                    TableName = "items",                   
                }
            },
            {
                "TradingApi", new DatabaseConfiguration
                {
                    Name = "TradingApi",
                    Host = "ubuyfirst-item-data.cluster-ckjfvcdemxab.us-west-2.rds.amazonaws.com",
                    Port = "5432",
                    DatabaseName = "postgres",
                    Username = "postgres",
                    Password = "NCHJu5OO6T79kVuJ0UOzu1LunAhu5r40P2jzkw5LfH7ft6aBFU",
                    SchemaName = "public",
                    TableName = "trading_api_items",
                    Columns = new List<string>
                    {
                        // Basic Item Info
                        "item_id", "title", "subtitle", "condition_id", "condition_description",
                        
                        // Timing Info
                        "start_time", "end_time", "fetch_time", "delayed_hours", 
                        "duration_hours", "duration_days",
                        
                        // Price Info
                        "current_price", "buy_it_now_price", "converted_current_price",
                        "converted_buy_it_now_price",
                        
                        // Quantity Info
                        "quantity", "quantity_sold",
                        
                        // Seller Info
                        "seller_username", "seller_feedback_score", "seller_feedback_percent",
                        "seller_top_rated", "seller_registration_year", "seller_store_url",
                        
                        // Category Info
                        "primary_category_id", "primary_category_name",
                        "secondary_category_id", "secondary_category_name",
                        
                        // Listing Info
                        "listing_type", "best_offer_enabled", "best_offer_count",
                        "auto_pay","bid_count", "sold_as_bin",
                        "is_revised", "hit_count", "relisted_item_id", "relist_parent_id",
                        
                        // Product Details
                        "upc", "brand","model", "mpn", "isbn", "ean", "product_reference_id",
                        
                        // Returns Info
                        "returns_accepted", "returns_within",
                        
                        // Location Info
                        "country", "ship_to_locations", "ship_to_location_count",
                        
                        // Additional Info
                        "picture_urls", "picture_count", "sku", "store_category_id",
                        
                        // Sale Status
                        "is_bin_listing", "is_auction_listing", "is_sold",
                        
                        // Item Specifics (stored as JSONB)
                        "item_specifics",
                        
                        // Description
                        "description"
                    }
                }
            }
        };

        public static DatabaseConfiguration GetConfiguration(string name)
        {
            if (Configurations.TryGetValue(name, out var config))
                return config;
            
            throw new KeyNotFoundException($"Database configuration '{name}' not found");
        }

        public static IEnumerable<string> GetAvailableConfigurations()
        {
            return Configurations.Keys;
        }
    }
}
