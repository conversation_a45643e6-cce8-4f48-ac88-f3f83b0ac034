﻿using System.Collections.Concurrent;
using eBay.Service.Core.Sdk;

namespace ItemScraper.Terms
{
    public class SearchTerm
    {
        public string Guid;

        public void Import(string row, out bool Success)
        {
            Success = false;
            var cells = row.Split(',');
            if (cells.Length < 9)
                throw new ArgumentException("Too few parameters:\n" + row);

            for (int i = 0; i < cells.Length; i++)
            {
                if (string.IsNullOrEmpty(cells[i]) && i != 2 && i != 1)
                {
                    throw new ArgumentException("Some fields are missing for this search parameter:\n" + row);
                }
            }

            Alias = cells[0];
            Keyword = cells[1];
            CategoryId = cells[2];
            PriceMin = decimal.Parse(cells[3]);
            PriceMax = decimal.Parse(cells[4]);
            DateEndEarliest = DateTime.Parse(cells[5]);
            DateEndLatest = DateTime.Parse(cells[6]);
            MaxItems = int.Parse(cells[7]);
            if (cells[8].ToLower() == "yes")
            {
                Auction = true;
            }

            if (cells[9].ToLower() == "yes")
            {
                BIN = true;
            }

            if (cells[10].ToLower() == "yes")
            {
                IncludeUnsold = true;
            }
            else
            {
                IncludeUnsold = false;
            }

            if (cells[11].ToLower() == "yes")
            {
                SkipItemSpecifics = false;
            }
            else
            {
                SkipItemSpecifics = true;
            }

            Success = true;
        }

        public string BasicSerpUrl
        {
            get
            {
                var url = "https://" +
                          Program.EbayDomain +
                          "/sch/i.html?_from=R40|R40"
                          + "&_sadis=15"
                          + "&_mPrRngCbx=1"
                          + "&_skc=0&rt=nc"
                          + "&_fcid=1"
                          + "&_pos=90210"
                          + "&_sacat=" + CategoryId
                          + "&_nkw=" + Keyword
                          + "&_fsrp=1"
                          + "&LH_PrefLoc=2" //2-Worldwide
                          + "&_dmd=2"
                          + "&_ipg=240"
                          + "&LH_Complete=1";

                if (Auction != true || BIN != true)
                {
                    if (Auction) url += "&LH_Auction=1";

                    if (BIN) url += "&LH_BIN=1";
                }

                if (!IncludeUnsold) url += "&LH_Sold=1";
                //_stpos=90210
                //& _salic = 1
                //&LH_LocatedIn=1
                //&LH_Time = 1
                //"&_fsradio2=%26LH_PrefLoc%3D1
                //"&LH_SubLocation=1"
                //&_sargn=-1%26saslc%3D1
                //"&_pos=90210
                //&_stpos=90210"
                //+ "&_udlo=1"
                //+ "&_udhi=100000"
                //"&_samilow=&_samihi="

                //"&_ftrt=903
                //&_ftrv=" + StartedWithinHours;
                //condition
                // url += "&LH_ItemCondition=3";
                //quantity
                //url += "&LH_MIL=1&_samilow=1&_samihi=1";;
                ;
                return url;
            }
        }

        public ApiContext ApiContext;
        public string Alias { get; set; }

        public string MongoCollectionName => $"{Alias} {Guid}";

        public string Keyword { get; set; }

        public decimal PriceMin { get; set; }

        public decimal PriceMax { get; set; }

        public int MaxItems { get; set; }

        public DateTime DateEndEarliest { get; set; }

        public DateTime DateEndLatest { get; set; }

        public string CategoryId { get; set; }

        public int StartedWithin { get; set; }

        public bool Auction { get; set; }

        public bool BIN { get; set; }

        public bool IncludeUnsold { get; set; }
        public List<string> SpecificsListNonUpdating { get; set; }
        public DateTime MinLastStartDateTarget { get; set; }
        public DateTime MaxCategoryStartDateToSave { get; set; }
        public Dictionary<string, HashSet<long>> DateItemDict { get; set; }

        public ConcurrentDictionary<string, byte> SpecificsList;

        public bool SkipItemSpecifics;
    }
}