﻿using System.Text;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;

public class EbayApiClient
{
    private readonly HttpClient _httpClient;
    private const string EbayApiUrl = "https://api.ebay.com/wsapi";
    private const string EbayAuthToken = "v^1.1#i^1#f^0#p^3#I^3#r^1#t^Ul4xMF83OkMzNzhGMkI3NkFFRjI5MTJGRjhDRTM0RjZBRERGMDlGXzFfMSNFXjI2MA==";

    public EbayApiClient(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<string> GetItemAsync(string itemId, bool includeItemSpecifics, ApiContext apiContext, DetailLevelCodeType[] detailLevelList)
    {
        var detailLevels = string.Empty;
        if (detailLevelList != null && detailLevelList.Length > 0)
        {
            foreach (var detail in detailLevelList)
            {
                detailLevels += $"<DetailLevel>{detail}</DetailLevel>\n            ";
            }
        }
        else
        {
            detailLevels = "<DetailLevel>ReturnAll</DetailLevel>\n            ";
        }

        var itemSpecificsRequest = includeItemSpecifics ? "<IncludeItemSpecifics>true</IncludeItemSpecifics>" : "";
        
        var soapEnvelope = $@"
<s:Envelope xmlns:s=""http://schemas.xmlsoap.org/soap/envelope/"">
    <s:Header>
        <h:RequesterCredentials xmlns:h=""urn:ebay:apis:eBLBaseComponents"" xmlns=""urn:ebay:apis:eBLBaseComponents"" xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"">
            <eBayAuthToken>{(apiContext?.ApiCredential?.eBayToken ?? EbayAuthToken)}</eBayAuthToken>
        </h:RequesterCredentials>
    </s:Header>
    <s:Body xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"" xmlns:xsd=""http://www.w3.org/2001/XMLSchema"">
        <GetItemRequest xmlns=""urn:ebay:apis:eBLBaseComponents"">
            {detailLevels}
            {itemSpecificsRequest}
            <MessageID>{Guid.NewGuid()}</MessageID>
            <Version>1131</Version>
            <ItemID>{itemId}</ItemID>
        </GetItemRequest>
    </s:Body>
</s:Envelope>";

        var content = new StringContent(soapEnvelope, Encoding.UTF8, "text/xml");
        content.Headers.Add("SOAPAction", "");

        var requestMessage = new HttpRequestMessage(HttpMethod.Post, $"{EbayApiUrl}?callname=GetItem&siteid=0&client=netsoap")
        {
            Content = content
        };

        // Set the 'Cache-Control' header in the request, not the content
        requestMessage.Headers.Add("Cache-Control", "no-cache, max-age=0");

        var response = await _httpClient.SendAsync(requestMessage);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadAsStringAsync();
    }
}
