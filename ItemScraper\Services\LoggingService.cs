namespace ItemScraper.Services;

public class LogStats
{
    public static int CurrentTermIndex { get; set; }
    public static int TotalTerms { get; set; }
    public static int CurrentUrlSetIndex { get; set; }
    public static int TotalUrlSets { get; set; }
    public static int CurrentItemIndex { get; set; }
    public static int TotalItems { get; set; }
    public static int TotalItemsFound { get; set; }
    public static int TotalUrlsDownloaded { get; set; }
    public static string CurrentCategoryId { get; set; }
}

public class LoggingService
{
    private static readonly object _consoleLock = new object();

    public void LogProgress()
    {
        lock (_consoleLock)
        {
            try
            {
                var logUpdate =
                    $"Found {LogStats.TotalItemsFound} items from {LogStats.TotalUrlsDownloaded} urls.".PadRight(50, ' ') + "\r\n"
                    + $"Term: [{LogStats.CurrentCategoryId}]  {LogStats.CurrentTermIndex + 1}/{LogStats.TotalTerms}".PadRight(50, ' ') + "\r\n"
                    + $"UrlSet: {LogStats.CurrentUrlSetIndex + 1}/{LogStats.TotalUrlSets}".PadRight(50, ' ') + "\r\n"
                    + $"Item: {LogStats.CurrentItemIndex}/{LogStats.TotalItems}".PadRight(50, ' ') + "\r\n";

                int currentCursorTop = Console.CursorTop;
                Console.Write(logUpdate);
                Console.SetCursorPosition(0, currentCursorTop);
            }
            catch (Exception e)
            {
                LogError("Console error:", e);
            }
        }
    }

    public void LogMessage(string message)
    {
        lock (_consoleLock)
        {
            Console.WriteLine($"{DateTime.UtcNow:s} - {message}");
        }
    }

    public void LogError(string message, Exception ex)
    {
        lock (_consoleLock)
        {
            Console.WriteLine($"{DateTime.UtcNow:s} - ERROR: {message}");
            Console.WriteLine(ex);
            System.Diagnostics.Debug.WriteLine($"{DateTime.UtcNow:s} - ERROR: {message}");
            System.Diagnostics.Debug.WriteLine(ex);
        }
    }
}

