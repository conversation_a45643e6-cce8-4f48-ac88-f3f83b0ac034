﻿using System.Diagnostics;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;
using System.Xml;
using System.Xml.Serialization;

// ReSharper disable UnusedMember.Global

namespace Seer.Tools
{
    public static class Serializator
    {
        public static MemoryStream Object2Stream(XmlSerializer xml, object obj)
        {
            var memStream = new MemoryStream();
            xml.Serialize(memStream, obj);
            memStream.Flush();
            memStream.Position = 0;
            return memStream;
        }

        public static T Stream2Object<T>(byte[] stream)
        {
            var xmlSerializer = new XmlSerializer(typeof(T));
            T deserialized = default;
            if (stream == null)
                return deserialized;

            using var memoryStream = new MemoryStream();
            memoryStream.Write(stream, 0, stream.Length);
            memoryStream.Position = 0;
            var sr = new StreamReader(memoryStream);

            var xml = sr.ReadToEnd();
            if (xml.Length > 10)
            {
                //xmlSerializer.Deserialize(new StringReader(xml));
                deserialized = (T)xmlSerializer.Deserialize(new StringReader(xml));
            }

            return deserialized;
        }

        public static T Str2Object<T>(string xml)
        {
            var xmlSerializer = new XmlSerializer(typeof(T));
            T deserialized = default;
            if (string.IsNullOrWhiteSpace(xml))
                return deserialized;

            if (xml.Length <= 10)
                return deserialized;

            //xmlSerializer.Deserialize(new StringReader(xml));
            deserialized = (T)xmlSerializer.Deserialize(new StringReader(xml));

            return deserialized;
        }

        public static T FromXml<T>(string rootName, string fileName) where T : class, new()
        {
            var serializer = new XmlSerializer(typeof(T), new XmlRootAttribute(rootName));
            TextReader reader = new StreamReader(fileName);
            return serializer.Deserialize(reader) as T;
        }

        public static string Encrypt(string toEncrypt, bool useHashing)
        {
            if (toEncrypt == null)
            {
                return "";
            }

            byte[] keyArray;
            var toEncryptArray = Encoding.UTF8.GetBytes(toEncrypt);
            const string Key = "3243243346578543211234";
            if (useHashing)
            {
                var hashMD5 = MD5.Create();
                keyArray = hashMD5.ComputeHash(Encoding.UTF8.GetBytes(Key));
                hashMD5.Clear();
            }
            else
            {
                keyArray = Encoding.UTF8.GetBytes(Key);
            }

            using var tDes = TripleDES.Create();
            tDes.Key = keyArray;
            tDes.Mode = CipherMode.ECB;
            tDes.Padding = PaddingMode.PKCS7;
            var cTransform = tDes.CreateEncryptor();
            var resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            tDes.Clear();
            return Convert.ToBase64String(resultArray, 0, resultArray.Length);
        }

        public static string Decrypt(string cypherString, bool useHashing)
        {
            try
            {
                byte[] keyArray;
                if (cypherString == null)
                    return "";

                var toDecryptArray = Convert.FromBase64String(cypherString);
                //byte[] toEncryptArray = Convert.FromBase64String(cypherString);
                //System.Configuration.AppSettingsReader settingReader = new     AppSettingsReader();
                const string Key = "3243243346578543211234";
                if (useHashing)
                {
                    var hashMD5 = MD5.Create();
                    keyArray = hashMD5.ComputeHash(Encoding.UTF8.GetBytes(Key));
                    hashMD5.Clear();
                }
                else
                {
                    keyArray = Encoding.UTF8.GetBytes(Key);
                }

                using var tDes = TripleDES.Create();
                tDes.Key = keyArray;
                tDes.Mode = CipherMode.ECB;
                tDes.Padding = PaddingMode.PKCS7;

                var cTransform = tDes.CreateDecryptor();
                try
                {
                    var resultArray = cTransform.TransformFinalBlock(toDecryptArray, 0, toDecryptArray.Length);
                    tDes.Clear();
                    return Encoding.UTF8.GetString(resultArray, 0, resultArray.Length);
                }
                catch (Exception ex)
                {
                    //MessageBox.Show(@"Decrypting:", ex.Message);
                }
            }
            catch (Exception)
            {
                return "";
            }

            return "";
        }

        public static string ObjectToXml(object o)
        {
            var sw = new StringWriter();
            XmlTextWriter tw = null;
            try
            {
                var serializer = new XmlSerializer(o.GetType());
                tw = new XmlTextWriter(sw);
                serializer.Serialize(tw, o);
            }
            catch (Exception)
            {
                //Handle Exception Code
            }
            finally
            {
                sw.Close();
                tw?.Close();
            }

            return sw.ToString();
        }

        public static object Xml2Object(string xml, Type objectType)
        {
            StringReader strReader = null;
            XmlTextReader xmlReader = null;
            object obj = null;
            try
            {
                strReader = new StringReader(xml);
                var serializer = new XmlSerializer(objectType);
                xmlReader = new XmlTextReader(strReader);
                obj = serializer.Deserialize(xmlReader);
            }
            catch (Exception)
            {
                //Handle Exception Code
            }
            finally
            {
                xmlReader?.Close();
                strReader?.Close();
            }

            return obj;
        }

        //public static string CompressObject(object data)
        //{
        //    IFormatter formatter = new BinaryFormatter();
        //    using (var ms = new MemoryStream())
        //    {
        //        formatter.Serialize(ms, data);
        //        ms.Seek(0, SeekOrigin.Begin);
        //        MemoryStream compressedStream = new MemoryStream();
        //        {
        //            using (var gZipStream = new GZipStream(compressedStream, CompressionMode.Compress, true))
        //            {
        //                ms.CopyTo(gZipStream);
        //            }

        //            compressedStream.Seek(0, SeekOrigin.Begin);
        //            var compressedData = new byte[compressedStream.Length];
        //            compressedStream.Read(compressedData, 0, compressedData.Length);

        //            var gZipBuffer = new byte[compressedData.Length + 4];
        //            Buffer.BlockCopy(compressedData, 0, gZipBuffer, 4, compressedData.Length);
        //            Buffer.BlockCopy(BitConverter.GetBytes(ms.Length), 0, gZipBuffer, 0, 4);
        //            var str = Convert.ToBase64String(gZipBuffer);
        //            return str;
        //        }
        //    }
        //}

        //public static object DecompressObject(string compressedObj)
        //{
        //    byte[] gZipBuffer = Convert.FromBase64String(compressedObj);
        //    IFormatter formatter = new BinaryFormatter();
        //    {
        //        using (MemoryStream compressedStream = new MemoryStream())
        //        {
        //            compressedStream.Write(gZipBuffer, 4, gZipBuffer.Length - 4);
        //            compressedStream.Position = 0;

        //            using (var uncompressedStream = new MemoryStream())
        //            {
        //                using (var gZipStream = new GZipStream(compressedStream, CompressionMode.Decompress, true))
        //                {
        //                    gZipStream.CopyTo(uncompressedStream);
        //                }

        //                uncompressedStream.Seek(0, SeekOrigin.Begin);
        //                return formatter.Deserialize(uncompressedStream);
        //            }
        //        }
        //    }
        //}

        public static string DeflateAndEncode(string str)
        {
            var bytes = Encoding.UTF8.GetBytes(str);
            using var output = new MemoryStream();
            using (var zip = new DeflateStream(output, CompressionMode.Compress))
            {
                zip.Write(bytes, 0, bytes.Length);
            }

            var base64 = Convert.ToBase64String(output.ToArray());

            return base64;
        }

        
        private static string DecodeAndInflate(string str)
        {
            var utf8 = Encoding.UTF8;
            var bytes = Convert.FromBase64String(str);
            using var output = new MemoryStream();
            using var input = new MemoryStream(bytes);
            using (var unzip = new DeflateStream(input, CompressionMode.Decompress))
            {
                unzip.CopyTo(output, bytes.Length);
                unzip.Close();
            }

            return utf8.GetString(output.ToArray());

        }

        public static string CompressString(string text)
        {
            var buffer = Encoding.UTF8.GetBytes(text);
            var memoryStream = new MemoryStream();
            using (var gZipStream = new GZipStream(memoryStream, CompressionMode.Compress, true))
            {
                gZipStream.Write(buffer, 0, buffer.Length);
            }

            memoryStream.Position = 0;
            var compressedData = new byte[memoryStream.Length];
            var gZipBuffer = new byte[compressedData.Length + 4];
            memoryStream.Read(compressedData, 0, compressedData.Length);
            Buffer.BlockCopy(compressedData, 0, gZipBuffer, 4, compressedData.Length);
            Buffer.BlockCopy(BitConverter.GetBytes(buffer.Length), 0, gZipBuffer, 0, 4);
            var compressString = Convert.ToBase64String(gZipBuffer);
            return compressString;
        }

        public static string DecompressString(string compressedText)
        {
            var gZipBuffer = Convert.FromBase64String(compressedText);
            using var memoryStream = new MemoryStream();
            var dataLength = BitConverter.ToInt32(gZipBuffer, 0);
            memoryStream.Write(gZipBuffer, 4, gZipBuffer.Length - 4);

            var buffer = new byte[dataLength];

            memoryStream.Position = 0;
            using (var gZipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
            {
                var totalRead = 0;
                while (totalRead < buffer.Length)
                {
                    var bytesRead = gZipStream.Read(buffer, totalRead, buffer.Length - totalRead);
                    if (bytesRead == 0)
                        break;
                    totalRead += bytesRead;
                }
            }

            return Encoding.UTF8.GetString(buffer);
        }

        public static T ParseXml<T>(this string @this) where T : class
        {
            if (string.IsNullOrEmpty(@this))
                return null;

            var xmlReaderSettings = new XmlReaderSettings
            {
                ConformanceLevel = ConformanceLevel.Auto
            };
            try
            {
                var xmlSerializer = new XmlSerializer(typeof(T));
                var stream = ToStream(@this.Trim());
                var reader = XmlReader.Create(stream, xmlReaderSettings);
                var deserialize = xmlSerializer.Deserialize(reader) as T;
                reader.Dispose();
                return deserialize;

            }
            catch (Exception ex)
            {
                Debug.WriteLine("Failed to deserialize XML" + ex.Message);
            }

            return null;
        }

        public static T ParseXmlWithNamespace<T>(this string xmlContent, string defaultNamespace) where T : class
        {
    
            Type[] extraTypes = new Type[0];

            // Create XmlAttributeOverrides to customize serialization behavior
            XmlAttributeOverrides overrides = new XmlAttributeOverrides();

            // Create XmlRootAttribute to specify root element properties
            XmlRootAttribute rootAttribute = new XmlRootAttribute()
            {
                Namespace = defaultNamespace,
                IsNullable = true
            };


            if (!string.IsNullOrEmpty(xmlContent))
            {
                XmlSerializer serializer = new XmlSerializer(typeof(T),defaultNamespace);
                using (TextReader reader = new StringReader(xmlContent))
                using (XmlReader customReader = new CustomXmlTextReader(reader, defaultNamespace))
                {
                    return (T)serializer.Deserialize(customReader);
                }
            }

            return null;
        }
        public static string SerializeXmlWithNamespace<T>(this T obj, string defaultNamespace) where T : class
        {
            if (obj == null)
            {
                throw new ArgumentNullException(nameof(obj));
            }
          
            XmlSerializer serializer = new XmlSerializer(typeof(T), defaultNamespace);
            using (StringWriter stringWriter = new StringWriter())
            {
                using (XmlWriter xmlWriter = XmlWriter.Create(stringWriter))
                {
                    serializer.Serialize(xmlWriter, obj);
                    return stringWriter.ToString();
                }
            }
        }

        public class CustomXmlTextReader : XmlTextReader
        {
            private readonly string _defaultNamespace;
            XmlReaderSettings settings = new XmlReaderSettings
            {
                IgnoreWhitespace = true,
                IgnoreComments = true,
                IgnoreProcessingInstructions = true,
            };
            public CustomXmlTextReader(TextReader reader, string defaultNamespace) : base(reader)
            {
                _defaultNamespace = defaultNamespace;
                
            }

            public override string NamespaceURI
            {
                get { return string.IsNullOrEmpty(base.NamespaceURI) ? _defaultNamespace : base.NamespaceURI; }
            }
        }

        private static Stream ToStream(this string @this)
        {
            var stream = new MemoryStream();
            var writer = new StreamWriter(stream);
            writer.Write(@this);
            writer.Flush();
            stream.Position = 0;
            return stream;
        }
    }
}
