﻿using System;


#pragma warning disable IDE1006
#pragma warning disable
// ReSharper disable All

namespace uBuyFirst.API.ShoppingAPI
{
    public class ShoppingAPIJson
    {
        public class GetMultipleItemsResponseType
        {
            public DateTime Timestamp { get; set; }
            public string Ack { get; set; }
            public Error[] Errors { get; set; }
            public string Build { get; set; }
            public string Version { get; set; }

            public SimpleItemType[] Item { get; set; }
        }


        public class Error
        {
            public string ShortMessage { get; set; }
            public string LongMessage { get; set; }
            public string ErrorCode { get; set; }
            public string SeverityCode { get; set; }
            public Errorparameter[] ErrorParameters { get; set; }
            public string ErrorClassification { get; set; }
        }

        public class Errorparameter
        {
            public string Value { get; set; }
            public string ParamID { get; set; }
        }

        public class SimpleItemType
        {
            public bool BestOfferEnabled { get; set; }
            public string ItemID { get; set; }
            public DateTime EndTime { get; set; }
            public DateTime StartTime { get; set; }
            public string ViewItemURLForNaturalSearch { get; set; }
            public string ListingType { get; set; }
            public string Location { get; set; }
            public string[] PaymentMethods { get; set; }
            public string GalleryURL { get; set; }
            public string[] PictureURL { get; set; }
            public string PostalCode { get; set; }
            public string PrimaryCategoryID { get; set; }
            public string PrimaryCategoryName { get; set; }
            public int Quantity { get; set; }
            public Seller Seller { get; set; }
            public int BidCount { get; set; }
            public Convertedcurrentprice ConvertedCurrentPrice { get; set; }
            public Currentprice CurrentPrice { get; set; }
            public string ListingStatus { get; set; }
            public int QuantitySold { get; set; }
            public string[] ShipToLocations { get; set; }
            public string Site { get; set; }
            public string TimeLeft { get; set; }
            public string Title { get; set; }
            public int HitCount { get; set; }
            public string PrimaryCategoryIDPath { get; set; }
            public Storefront Storefront { get; set; }
            public string Country { get; set; }
            public Returnpolicy ReturnPolicy { get; set; }
            public bool AutoPay { get; set; }
            public string[] PaymentAllowedSite { get; set; }
            public bool IntegratedMerchantCreditCardEnabled { get; set; }
            public int HandlingTime { get; set; }
            public int ConditionID { get; set; }
            public string ConditionDisplayName { get; set; }
            public string QuantityAvailableHint { get; set; }
            public int QuantityThreshold { get; set; }
            public string[] ExcludeShipToLocation { get; set; }
            public bool GlobalShipping { get; set; }
            public int QuantitySoldByPickupInStore { get; set; }
            public bool NewBestOffer { get; set; }
            public string ConditionDescription { get; set; }
            public string SKU { get; set; }
            public Buyitnowprice BuyItNowPrice { get; set; }
            public bool BuyItNowAvailable { get; set; }
            public Convertedbuyitnowprice ConvertedBuyItNowPrice { get; set; }
            public Minimumtobid MinimumToBid { get; set; }
            public Productid ProductID { get; set; }
            public string Subtitle { get; set; }
            public bool TopRatedListing { get; set; }
            public Charity Charity { get; set; }
            public bool AvailableForPickupDropOff { get; set; }
            public string SecondaryCategoryID { get; set; }
            public string SecondaryCategoryName { get; set; }
            public string SecondaryCategoryIDPath { get; set; }
            public int LotSize { get; set; }
            public Discountpriceinfo DiscountPriceInfo { get; set; }
            public Highbidder HighBidder { get; set; }
            public bool ReserveMet { get; set; }

            public int WatchCount { get; set; }

            public string StoreName { get; set; }
            public string Description { get; set; }
        }

        public class Seller
        {
            public string UserID { get; set; }
            public string FeedbackRatingStar { get; set; }
            public int FeedbackScore { get; set; }
            public float PositiveFeedbackPercent { get; set; }
            public bool TopRatedSeller { get; set; }

            public bool FeedbackPrivate { get; set; }
            public bool NewUser { get; set; }
            public string SellerLevel { get; set; }
        }


        public class Convertedcurrentprice
        {
            public double Value { get; set; }
            public string CurrencyID { get; set; }
        }

        public class Currentprice
        {
            public double Value { get; set; }
            public string CurrencyID { get; set; }
        }

        public class Storefront
        {
            public string StoreURL { get; set; }
            public string StoreName { get; set; }
        }

        public class Returnpolicy
        {
            public string ReturnsWithin { get; set; }
            public string ReturnsAccepted { get; set; }
            public string ShippingCostPaidBy { get; set; }
            public string Description { get; set; }
            public string InternationalReturnsWithin { get; set; }
            public string InternationalReturnsAccepted { get; set; }
            public string InternationalShippingCostPaidBy { get; set; }
            public string Refund { get; set; }
            public string InternationalRefund { get; set; }
        }

        public class Buyitnowprice
        {
            public double Value { get; set; }
            public string CurrencyID { get; set; }
        }

        public class Convertedbuyitnowprice
        {
            public double Value { get; set; }
            public string CurrencyID { get; set; }
        }

        public class Minimumtobid
        {
            public double Value { get; set; }
            public string CurrencyID { get; set; }
        }

        public class Productid
        {
            public string Value { get; set; }
            public string Type { get; set; }
        }

        public class Charity
        {
            public string CharityID { get; set; }
            public string CharityName { get; set; }
            public int CharityNumber { get; set; }
            public double DonationPercent { get; set; }
            public string Mission { get; set; }
            public string LogoURL { get; set; }
            public string Status { get; set; }
        }

        public class Discountpriceinfo
        {
            public Originalretailprice OriginalRetailPrice { get; set; }
            public string PricingTreatment { get; set; }
            public bool SoldOneBay { get; set; }
            public bool SoldOffeBay { get; set; }
        }

        public class Originalretailprice
        {
            public double Value { get; set; }
            public string CurrencyID { get; set; }
        }

        public class Highbidder
        {
            public string UserID { get; set; }
            public bool FeedbackPrivate { get; set; }
            public string FeedbackRatingStar { get; set; }
            public int FeedbackScore { get; set; }

            public bool NewUser { get; set; }
            public double PositiveFeedbackPercent { get; set; }
            public string RegistrationSite { get; set; }
            public string SellerBusinessType { get; set; }
            public string SellerLevel { get; set; }
            public bool TopRatedSeller { get; set; }
            public bool UserAnonymized { get; set; }
        }
    }
}
