using System.Collections.Concurrent;
using System.Globalization;
using System.Text.RegularExpressions;
using HtmlAgilityPack;

namespace ItemScraper
{
    public enum SerpFormat
    {
        OldFormat,  // s-item based format
        NewFormat   // s-card based format
    }

    public static class SerpParser
    {
        private static ConcurrentDictionary<string, byte> _uniqItemIds;
        public static int ParsedItems;

        // Format detection metrics
        public static int OldFormatItemsCount = 0;
        public static int NewFormatItemsCount = 0;


        public class SerpItem
        {
            public long ItemID { get; set; }
            public DateTime Date { get; set; }
            public decimal Price { get; set; }
            public decimal ShippingPrice { get; set; }
            public bool BestOfferSold { get; set; }

            public SerpItem(long itemID, DateTime date, decimal price, decimal shippingPrice, bool bestOfferSold)
            {
                ItemID = itemID;
                Date = date;
                Price = price;
                ShippingPrice = shippingPrice;
                BestOfferSold = bestOfferSold;
            }

        }
        public static List<SerpItem> ParseIdTimeFromSerp(string html)
        {
            var idTimeList = new List<SerpItem>();

            if (string.IsNullOrWhiteSpace(html))
                return idTimeList;

            var doc = new HtmlDocument();
            doc.LoadHtml(html);

            var parentNode = doc.DocumentNode.SelectSingleNode("//div[contains(@class,'srp-river-results')]");
            if (parentNode == null)
                return idTimeList;

            var htmlNodeCollection = parentNode.SelectNodes(".//li");
            for (var index = 0; index < htmlNodeCollection.Count; index++)
            {
                var liNode = htmlNodeCollection[index];
                if (IsRewriteStartMarker(liNode))
                    break;

                if (!IsValidItemNode(liNode))
                    continue;

                try
                {
                    var href = ExtractHref(liNode);
                    if (string.IsNullOrEmpty(href)) continue;

                    if (!long.TryParse(SerpUrlGenerator.RegexValue(href, @"itm/(\d{10,12})"), out var itemID))
                        continue;

                    var date = ExtractDate(liNode);
                    if (date == DateTime.MinValue)
                        continue;

                    var price = ExtractPrice(liNode);
                    if (price == 0)
                        continue;

                    var shippingPrice = ExtractShippingPrice(liNode);

                    var soldAsBestOffer = ExtractBestOfferAccepted(liNode);

                    idTimeList.Add(new SerpItem(itemID, date, price, shippingPrice, soldAsBestOffer));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error parsing item: {ex.Message}");
                }
            }

            return idTimeList;
        }

        private static bool IsRewriteStartMarker(HtmlNode liNode)
        {
            var classes = liNode.GetAttributeValue("class", "").Split(' ');
            bool hasRequiredClasses = classes.Contains("srp-river-answer") && classes.Contains("srp-river-answer--REWRITE_START");
            bool containsMatchingText = liNode.InnerText.Contains("Results matching fewer words");

            return hasRequiredClasses && containsMatchingText;
        }


        private static SerpFormat DetectSerpFormat(HtmlNode liNode)
        {
            var classes = liNode.GetAttributeValue("class", "").Split(' ');

            // Check for new format first (s-card)
            if (classes.Contains("s-card"))
                return SerpFormat.NewFormat;

            // Default to old format (s-item)
            return SerpFormat.OldFormat;
        }

        private static bool IsValidItemNode(HtmlNode liNode)
        {
            var classes = liNode.GetAttributeValue("class", "").Split(' ');
            return classes.Contains("s-item") || classes.Contains("s-card");
        }

        private static string ExtractHrefOld(HtmlNode liNode)
        {
            var linkNode = liNode.SelectSingleNode(".//a[contains(@class, 's-item__link')]");
            return linkNode?.GetAttributeValue("href", string.Empty);
        }

        private static string ExtractHrefNew(HtmlNode liNode)
        {
            // Try su-link first (title link)
            var linkNode = liNode.SelectSingleNode(".//a[contains(@class, 'su-link')]");
            if (linkNode != null)
                return linkNode.GetAttributeValue("href", string.Empty);

            // Fallback to image-treatment link
            linkNode = liNode.SelectSingleNode(".//a[contains(@class, 'image-treatment')]");
            return linkNode?.GetAttributeValue("href", string.Empty);
        }

        private static DateTime ExtractDateOld(HtmlNode liNode)
        {
            var dateNode = liNode.SelectSingleNode(".//div[contains(@class, 's-item__caption--row')]/span[contains(@class, 's-item__caption--signal')]");
            var dateStr = dateNode?.InnerText.Trim().Replace("Ended ", "").Replace("Sold ", "").Trim();
            var date = DateTime.MinValue;

            if (dateStr.Contains("Verkauft"))
            {
                dateStr = dateStr.Replace("Verkauft ", "");
                date = str2date(dateStr.Trim(), "d. MMM yyyy");
            }
            else
            {
                date = str2date(dateStr, "MMM d, yyyy");
            }


            // Adjust the year if the date appears to be in the future
            if (date != DateTime.MinValue && DateTime.UtcNow.AddDays(5) < date)
            {
                //date = date.AddYears(-1);
            }

            return date;
        }

        private static DateTime ExtractDateNew(HtmlNode liNode)
        {
            // Look for date in the caption area with "Sold" text
            var dateNode = liNode.SelectSingleNode(".//span[contains(@class, 'su-styled-text') and contains(@class, 'positive') and contains(@class, 'default') and contains(text(), 'Sold')]");
            var dateStr = dateNode?.InnerText.Trim();

            if (string.IsNullOrEmpty(dateStr))
                return DateTime.MinValue;

            // Remove "Sold" prefix and extra spaces
            dateStr = dateStr.Replace("Sold", "").Trim();

            var date = DateTime.MinValue;

            // Try parsing common date formats
            if (DateTime.TryParse(dateStr, out date))
                return date;

            // Try specific format like "Mar 18, 2025"
            if (DateTime.TryParseExact(dateStr, "MMM d, yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
                return date;

            // Try format with day suffix like "Mar 18th, 2025"
            var cleanDateStr = Regex.Replace(dateStr, @"(\d+)(st|nd|rd|th)", "$1");
            if (DateTime.TryParseExact(cleanDateStr, "MMM d, yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
                return date;

            return DateTime.MinValue;
        }

        private static bool ExtractBestOfferAcceptedOld(HtmlNode liNode)
        {
            var priceNode = liNode.SelectSingleNode(".//span[contains(@class, 's-item__formatBestOfferAccepted')]");
            var bestOfferSold = priceNode != null;

            return bestOfferSold;
        }

        private static bool ExtractBestOfferAcceptedNew(HtmlNode liNode)
        {
            // In the new format, we need to look for best offer indicators
            // This might be in the price area or as a separate indicator
            // Since the provided example doesn't show this, we'll look for common patterns

            // Check for "Best Offer" text in various locations
            var bestOfferNodes = liNode.SelectNodes(".//span[contains(text(), 'Best Offer') or contains(text(), 'best offer')]");
            if (bestOfferNodes != null && bestOfferNodes.Count > 0)
                return true;

            // Check for "or Best Offer" in price-related areas
            var priceNodes = liNode.SelectNodes(".//span[contains(@class, 'su-styled-text')]");
            if (priceNodes != null)
            {
                foreach (var node in priceNodes)
                {
                    var text = node.InnerText?.ToLower();
                    if (!string.IsNullOrEmpty(text) && (text.Contains("best offer") || text.Contains("or best")))
                        return true;
                }
            }

            return false;
        }

        private static decimal ExtractPriceOld(HtmlNode liNode)
        {
            if (liNode == null)
                throw new ArgumentNullException(nameof(liNode));

            var priceNode = liNode.SelectSingleNode(".//span[contains(@class, 's-item__price')]");
            if (priceNode == null)
                return 0m;

            // Get and trim the raw text once.
            string rawText = priceNode.InnerText.Trim();
            string priceStr;

            // Check for EUR and apply the appropriate string replacements.
            if (rawText.Contains("EUR"))
            {
                // For EUR prices, remove "Verkauft ", "EUR", and format the number accordingly.
                priceStr = rawText
                    .Replace("Verkauft ", string.Empty)
                    .Replace("EUR", string.Empty)
                    .Replace(".", string.Empty)   // Remove thousands separator.
                    .Replace(",", ".");           // Convert decimal separator to invariant format.
            }
            else
            {
                // For non-EUR prices (assuming USD), remove "Sold ", "$" and commas.
                priceStr = rawText
                    .Replace("Sold ", string.Empty)
                    .Replace("$", string.Empty)
                    .Replace(",", string.Empty);
            }

            // Attempt to parse using InvariantCulture to ensure consistent number formatting.
            if (decimal.TryParse(priceStr, NumberStyles.Any, CultureInfo.InvariantCulture, out var price))
                return price;

            return 0m;
        }

        private static decimal ExtractPriceNew(HtmlNode liNode)
        {
            if (liNode == null)
                throw new ArgumentNullException(nameof(liNode));

            var priceNode = liNode.SelectSingleNode(".//span[contains(@class, 's-card__price')]");
            if (priceNode == null)
                return 0m;

            // Get and trim the raw text once.
            string rawText = priceNode.InnerText.Trim();
            string priceStr;

            // Check for EUR and apply the appropriate string replacements.
            if (rawText.Contains("EUR"))
            {
                // For EUR prices, remove "EUR", and format the number accordingly.
                priceStr = rawText
                    .Replace("EUR", string.Empty)
                    .Replace(".", string.Empty)   // Remove thousands separator.
                    .Replace(",", ".");           // Convert decimal separator to invariant format.
            }
            else
            {
                // For USD prices, remove "$" and format the number accordingly.
                priceStr = rawText.Replace("$", string.Empty).Replace(",", string.Empty);
            }

            // Attempt to parse the cleaned price string as decimal.
            if (decimal.TryParse(priceStr.Trim(), NumberStyles.Number, CultureInfo.InvariantCulture, out decimal price))
                return price;

            return 0m;
        }

        private static decimal ExtractShippingPriceOld(HtmlNode liNode)
        {
            // Default shipping price to zero
            decimal shippingPrice = 0;

            // Select the node containing the shipping price information
            var priceNode = liNode.SelectSingleNode(".//span[contains(@class, 's-item__shipping s-item__logisticsCost')]");
            var priceStr = priceNode?.InnerText?.Trim();

            // Return 0 if the price string is null or indicates "Free" shipping
            if (string.IsNullOrEmpty(priceStr))
                return shippingPrice;
            if (priceStr.Contains("Free"))
                return shippingPrice;

            // Extract the numeric value using regex and attempt to parse it as decimal
            var match = Regex.Match(priceStr, @"\d+(\.\d{1,2})?");
            if (match.Success && decimal.TryParse(match.Value, out shippingPrice))
                return shippingPrice;

            return 0; // Default to 0 if parsing fails
        }

        private static decimal ExtractShippingPriceNew(HtmlNode liNode)
        {
            // Default shipping price to zero
            decimal shippingPrice = 0;

            // Look for shipping information in secondary text spans
            var shippingNodes = liNode.SelectNodes(".//span[contains(@class, 'su-styled-text') and contains(@class, 'secondary') and contains(@class, 'large')]");

            if (shippingNodes == null)
                return shippingPrice;

            foreach (var node in shippingNodes)
            {
                var text = node.InnerText?.Trim();
                if (string.IsNullOrEmpty(text))
                    continue;

                // Check for free shipping indicators
                if (text.Contains("Free delivery") || text.Contains("Free shipping"))
                    return 0;

                // Look for shipping cost patterns
                if (text.Contains("shipping") || text.Contains("delivery"))
                {
                    // Extract numeric value using regex
                    var match = Regex.Match(text, @"\$?(\d+(?:\.\d{1,2})?)");
                    if (match.Success && decimal.TryParse(match.Groups[1].Value, out shippingPrice))
                        return shippingPrice;
                }
            }

            return 0; // Default to 0 if no shipping info found
        }

        // Adaptive wrapper methods that detect format and call appropriate extraction method
        private static string ExtractHref(HtmlNode liNode)
        {
            var format = DetectSerpFormat(liNode);

            try
            {
                var result = format == SerpFormat.NewFormat ? ExtractHrefNew(liNode) : ExtractHrefOld(liNode);

                // If primary format fails, try the other format as fallback
                if (string.IsNullOrEmpty(result))
                {
                    result = format == SerpFormat.NewFormat ? ExtractHrefOld(liNode) : ExtractHrefNew(liNode);
                }

                return result;
            }
            catch
            {
                // If primary format throws exception, try the other format
                try
                {
                    return format == SerpFormat.NewFormat ? ExtractHrefOld(liNode) : ExtractHrefNew(liNode);
                }
                catch
                {
                    return string.Empty;
                }
            }
        }

        private static DateTime ExtractDate(HtmlNode liNode)
        {
            var format = DetectSerpFormat(liNode);

            try
            {
                var result = format == SerpFormat.NewFormat ? ExtractDateNew(liNode) : ExtractDateOld(liNode);

                // If primary format fails, try the other format as fallback
                if (result == DateTime.MinValue)
                {
                    result = format == SerpFormat.NewFormat ? ExtractDateOld(liNode) : ExtractDateNew(liNode);
                }

                return result;
            }
            catch
            {
                // If primary format throws exception, try the other format
                try
                {
                    return format == SerpFormat.NewFormat ? ExtractDateOld(liNode) : ExtractDateNew(liNode);
                }
                catch
                {
                    return DateTime.MinValue;
                }
            }
        }

        private static decimal ExtractPrice(HtmlNode liNode)
        {
            var format = DetectSerpFormat(liNode);

            try
            {
                var result = format == SerpFormat.NewFormat ? ExtractPriceNew(liNode) : ExtractPriceOld(liNode);

                // If primary format fails, try the other format as fallback
                if (result == 0m)
                {
                    result = format == SerpFormat.NewFormat ? ExtractPriceOld(liNode) : ExtractPriceNew(liNode);
                }

                return result;
            }
            catch
            {
                // If primary format throws exception, try the other format
                try
                {
                    return format == SerpFormat.NewFormat ? ExtractPriceOld(liNode) : ExtractPriceNew(liNode);
                }
                catch
                {
                    return 0m;
                }
            }
        }

        private static decimal ExtractShippingPrice(HtmlNode liNode)
        {
            var format = DetectSerpFormat(liNode);

            try
            {
                var result = format == SerpFormat.NewFormat ? ExtractShippingPriceNew(liNode) : ExtractShippingPriceOld(liNode);

                // For shipping price, 0 is a valid result (free shipping), so we don't use it as a fallback trigger
                // Only fallback on exceptions
                return result;
            }
            catch
            {
                // If primary format throws exception, try the other format
                try
                {
                    return format == SerpFormat.NewFormat ? ExtractShippingPriceOld(liNode) : ExtractShippingPriceNew(liNode);
                }
                catch
                {
                    return 0m;
                }
            }
        }

        private static bool ExtractBestOfferAccepted(HtmlNode liNode)
        {
            var format = DetectSerpFormat(liNode);

            try
            {
                var result = format == SerpFormat.NewFormat ? ExtractBestOfferAcceptedNew(liNode) : ExtractBestOfferAcceptedOld(liNode);

                // For boolean results, false is a valid result, so we don't use it as a fallback trigger
                // Only fallback on exceptions
                return result;
            }
            catch
            {
                // If primary format throws exception, try the other format
                try
                {
                    return format == SerpFormat.NewFormat ? ExtractBestOfferAcceptedOld(liNode) : ExtractBestOfferAcceptedNew(liNode);
                }
                catch
                {
                    return false;
                }
            }
        }

        public static DateTime str2date(string str, string format)
        {
            var date = new DateTime();
            if (!DateTime.TryParseExact(str, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
            {
                return new DateTime(1990, 1, 1);
            }

            return date;
        }
    }
}