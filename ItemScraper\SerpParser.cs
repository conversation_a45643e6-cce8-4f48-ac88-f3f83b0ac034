﻿using System.Collections.Concurrent;
using System.Globalization;
using System.Text.RegularExpressions;
using HtmlAgilityPack;

namespace ItemScraper
{
    public static class SerpParser
    {
        private static ConcurrentDictionary<string, byte> _uniqItemIds;
        public static int ParsedItems;


        public class SerpItem
        {
            public long ItemID { get; set; }
            public DateTime Date { get; set; }
            public decimal Price { get; set; }
            public decimal ShippingPrice { get; set; }
            public bool BestOfferSold { get; set; }

            public SerpItem(long itemID, DateTime date, decimal price, decimal shippingPrice, bool bestOfferSold)
            {
                ItemID = itemID;
                Date = date;
                Price = price;
                ShippingPrice = shippingPrice;
                BestOfferSold = bestOfferSold;
            }

        }
        public static List<SerpItem> ParseIdTimeFromSerp(string html)
        {
            var idTimeList = new List<SerpItem>();

            if (string.IsNullOrWhiteSpace(html))
                return idTimeList;

            var doc = new HtmlDocument();
            doc.LoadHtml(html);

            var parentNode = doc.DocumentNode.SelectSingleNode("//div[contains(@class,'srp-river-results')]");
            if (parentNode == null)
                return idTimeList;

            var htmlNodeCollection = parentNode.SelectNodes(".//li");
            for (var index = 0; index < htmlNodeCollection.Count; index++)
            {
                var liNode = htmlNodeCollection[index];
                if (IsRewriteStartMarker(liNode))
                    break;

                if (!IsValidItemNode(liNode))
                    continue;

                try
                {
                    var href = ExtractHref(liNode);
                    if (string.IsNullOrEmpty(href)) continue;

                    if (!long.TryParse(SerpUrlGenerator.RegexValue(href, @"itm/(\d{10,12})"), out var itemID))
                        continue;

                    var date = ExtractDate(liNode);
                    if (date == DateTime.MinValue)
                        continue;

                    var price = ExtractPrice(liNode);
                    if (price == 0)
                        continue;

                    var shippingPrice = ExtractShippingPrice(liNode);

                    var soldAsBestOffer = ExtractBestOfferAccepted(liNode);

                    idTimeList.Add(new SerpItem(itemID, date, price, shippingPrice, soldAsBestOffer));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error parsing item: {ex.Message}");
                }
            }

            return idTimeList;
        }

        private static bool IsRewriteStartMarker(HtmlNode liNode)
        {
            var classes = liNode.GetAttributeValue("class", "").Split(' ');
            bool hasRequiredClasses = classes.Contains("srp-river-answer") && classes.Contains("srp-river-answer--REWRITE_START");
            bool containsMatchingText = liNode.InnerText.Contains("Results matching fewer words");

            return hasRequiredClasses && containsMatchingText;
        }


        private static bool IsValidItemNode(HtmlNode liNode)
        {
            var classes = liNode.GetAttributeValue("class", "").Split(' ');
            return classes.Contains("s-item");
        }

        private static string ExtractHref(HtmlNode liNode)
        {
            var linkNode = liNode.SelectSingleNode(".//a[contains(@class, 's-item__link')]");
            return linkNode?.GetAttributeValue("href", string.Empty);
        }

        private static DateTime ExtractDate(HtmlNode liNode)
        {
            var dateNode = liNode.SelectSingleNode(".//div[contains(@class, 's-item__caption--row')]/span[contains(@class, 's-item__caption--signal')]");
            var dateStr = dateNode?.InnerText.Trim().Replace("Ended ", "").Replace("Sold ", "").Trim();
            var date = DateTime.MinValue;
            
            if (dateStr.Contains("Verkauft"))
            {
                dateStr = dateStr.Replace("Verkauft ", "");
                date = str2date(dateStr.Trim(), "d. MMM yyyy");
            }
            else
            {
                date = str2date(dateStr, "MMM d, yyyy");
            }


            // Adjust the year if the date appears to be in the future
            if (date != DateTime.MinValue && DateTime.UtcNow.AddDays(5) < date)
            {
                //date = date.AddYears(-1);
            }

            return date;
        }

        private static bool ExtractBestOfferAccepted(HtmlNode liNode)
        {
            var priceNode = liNode.SelectSingleNode(".//span[contains(@class, 's-item__formatBestOfferAccepted')]");
            var bestOfferSold = priceNode != null;

            return bestOfferSold;
        }

        private static decimal ExtractPrice(HtmlNode liNode)
        {
            if (liNode == null)
                throw new ArgumentNullException(nameof(liNode));

            var priceNode = liNode.SelectSingleNode(".//span[contains(@class, 's-item__price')]");
            if (priceNode == null)
                return 0m;

            // Get and trim the raw text once.
            string rawText = priceNode.InnerText.Trim();
            string priceStr;

            // Check for EUR and apply the appropriate string replacements.
            if (rawText.Contains("EUR"))
            {
                // For EUR prices, remove "Verkauft ", "EUR", and format the number accordingly.
                priceStr = rawText
                    .Replace("Verkauft ", string.Empty)
                    .Replace("EUR", string.Empty)
                    .Replace(".", string.Empty)   // Remove thousands separator.
                    .Replace(",", ".");           // Convert decimal separator to invariant format.
            }
            else
            {
                // For non-EUR prices (assuming USD), remove "Sold ", "$" and commas.
                priceStr = rawText
                    .Replace("Sold ", string.Empty)
                    .Replace("$", string.Empty)
                    .Replace(",", string.Empty);
            }

            // Attempt to parse using InvariantCulture to ensure consistent number formatting.
            if (decimal.TryParse(priceStr, NumberStyles.Any, CultureInfo.InvariantCulture, out var price))
                return price;

            return 0m;
        }

        private static decimal ExtractShippingPrice(HtmlNode liNode)
        {
            // Default shipping price to zero
            decimal shippingPrice = 0;

            // Select the node containing the shipping price information
            var priceNode = liNode.SelectSingleNode(".//span[contains(@class, 's-item__shipping s-item__logisticsCost')]");
            var priceStr = priceNode?.InnerText?.Trim();

            // Return 0 if the price string is null or indicates "Free" shipping
            if (string.IsNullOrEmpty(priceStr))
                return shippingPrice;
            if (priceStr.Contains("Free"))
                return shippingPrice;

            // Extract the numeric value using regex and attempt to parse it as decimal
            var match = Regex.Match(priceStr, @"\d+(\.\d{1,2})?");
            if (match.Success && decimal.TryParse(match.Value, out shippingPrice))
                return shippingPrice;

            return 0; // Default to 0 if parsing fails
        }




        public static DateTime str2date(string str, string format)
        {
            var date = new DateTime();
            if (!DateTime.TryParseExact(str, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out date))
            {
                return new DateTime(1990, 1, 1);
            }

            return date;
        }
    }
}