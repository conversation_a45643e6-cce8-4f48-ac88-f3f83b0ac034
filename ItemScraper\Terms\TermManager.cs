﻿namespace ItemScraper.Terms
{
    public class TermManager
    {
        public static List<SearchTerm> ImportTermsFromCsv(string filePath)
        {
            var kwList = new List<SearchTerm>();
            try
            {
                var currentDirectory = Directory.GetCurrentDirectory();
                var fullPath = Path.Combine(currentDirectory, filePath);
                Console.WriteLine(fullPath);
                var rows = File.ReadAllLines(fullPath);
                if (rows.Length > 1)
                {
                    for (int i = 1; i < rows.Count(); i++)
                    {
                        if (rows[i].Length < 10)
                            continue;
                        var kw = new SearchTerm();
                        bool kwIsGood;
                        kw.Import(rows[i], out kwIsGood);
                        if (kwIsGood)
                        {
                            kwList.Add(kw);
                        }
                        else
                        {
                            throw new ArgumentException("Row #" + (i + 2) + " - Skipped\n" + rows[i] + "\n");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new FileLoadException("Please, close SearchTerms.csv\n" + ex.Message);
            }

            return kwList;
        }
    }
}