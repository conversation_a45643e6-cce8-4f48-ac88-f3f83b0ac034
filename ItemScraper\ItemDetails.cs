﻿﻿namespace ItemScraper;

public class ItemDetails
{
    public long item_id { get; set; } // Changed from ItemID
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public DateTime FetchTime { get; set; }
    public decimal Price { get; set; } // Changed type to decimal
    public int QuantitySold { get; set; }
    public long Duration { get; set; } // Changed type to long
    public string BuyerName { get; set; }
    public int? BuyerFeedbackCount { get; set; }
    public bool BuyerFeedbackPrivate { get; set; }
    public string SellerName { get; set; }
    public int? SellerFeedbackCount { get; set; }
    public int LeafCategoryID { get; set; }
    public int ListingStatus { get; set; }
    
}