using Npgsql;
using ItemScraper.Configuration;
using ItemScraper.Services;
using System.Text.Json;
using NpgsqlTypes;

namespace ItemScraper
{
    public class DataAnalysisToDB
    {
        private readonly string _connectionString;
        private const int BatchSize = 5000;
        private readonly string _tableName;

        public DataAnalysisToDB(string tableName)
        {
            var dbConfig = DatabaseConfigurations.GetConfiguration("data_analysis");
            _connectionString = dbConfig.GetConnectionString();
            _tableName = tableName;
        }

        public async Task CreateTableIfNotExists()
        {
            using var conn = new NpgsqlConnection(_connectionString);
            await conn.OpenAsync();

            var sql = $@"
CREATE TABLE IF NOT EXISTS data_analysis.{_tableName} (
	search_name varchar(50) NULL,
	item_id int8 NOT NULL,
	category_id int4 NULL,
	title text NULL,
	
	price numeric(10, 2) NULL,
	shipping_price numeric(5, 2) NULL,
	shipping_type varchar(50) NULL,
	shipping_cost_paid_by_option varchar(50) NULL,
	dispatch_time_max int4 NULL,
	country_from varchar(50) NULL,
	country_ships_to text NULL,
	postal_code varchar(50) NULL,
	
	condition_id int2 NULL,
	duration_seconds int4 NULL,
	sub_title text NULL,
	condition_description varchar(1000) NULL,
	description text NULL,
	
	specifics jsonb NULL,
	
	brand varchar(250) NULL,  
    model varchar(250) NULL,
    MPN varchar(250) NULL,		
    UPC varchar(250) NULL,		
	product_reference_id varchar(250) NULL,		
		
	
	seller_name varchar(255) NULL,
	seller_feedback_count int4 NULL,
	seller_feedback_percent numeric(10, 2) NULL,	
	seller_feedback_private bool NULL,

    buyer_name varchar(255) NULL,
    buyer_feedback_count int4 NULL,
    buyer_feedback_percent numeric(10, 2) NULL,
    buyer_feedback_private bool NULL,

	buyer_protection varchar(50) NULL,

	quantity_total int4 NULL,
	quantity_sold int4 NULL,
	lot_size int4 NULL,
	bid_count int2 NULL,
	
	
	picture_count int2 NULL,
	best_offer bool NULL,
	get_it_fast bool NULL,
	relist_parent_id int8 NULL,
	
	refund_option varchar(50) NULL,
	returns_accepted_option varchar(50) NULL,
	returns_within_option varchar(50) NULL,
	revised bool NULL,
	top_rated_listing bool NULL,
	
	sold_as_bin bool NULL,
	bin_sold bool NULL,
	auction_sold bool NULL,
	bin_not_sold bool NULL,
	auction_not_sold bool NULL,
	best_offer_sold bool NULL,
	commit_to_buy bool NULL,
	
	
	listing_type varchar(50) NULL,
	
	pictures_url text NULL,
	time_posted timestamptz NULL,
	time_ended timestamptz NULL,

	updated_at timestamptz DEFAULT CURRENT_TIMESTAMP NULL,
	CONSTRAINT item_predict_{_tableName}_pkey PRIMARY KEY (item_id)
);
                ";

            await using var command = new NpgsqlCommand(sql, conn);
            await command.ExecuteNonQueryAsync();
        }

        public async Task InsertItems(List<ItemPredictData> items)
        {
            try
            {
                if (items == null || !items.Any()) return;

                await using var conn = new NpgsqlConnection(_connectionString);
                await conn.OpenAsync();

                var sql = $@"
                INSERT INTO data_analysis.{_tableName} (
                    item_id, title, best_offer, bid_count, brand, model, mpn, upc, buyer_protection,
                    category_id, condition_id, condition_description,
                    country_from, country_ships_to, description, dispatch_time_max,
                    seller_feedback_count, seller_feedback_percent, get_it_fast, listing_type,
                    lot_size, pictures_url, postal_code, quantity_total, quantity_sold,
                    refund_option, relist_parent_id, returns_accepted_option,
                    returns_within_option, revised, seller_name, shipping_cost_paid_by_option,
                    sub_title, time_ended, time_posted, top_rated_listing, sold_as_bin,
                    best_offer_sold, duration_seconds, picture_count, product_reference_id, price,
                    bin_sold, auction_sold, bin_not_sold, auction_not_sold,
                    commit_to_buy, shipping_type, shipping_price, specifics,
                    search_name, seller_feedback_private, buyer_name, buyer_feedback_count,
                    buyer_feedback_percent, buyer_feedback_private
                ) VALUES (
                    @ItemId, @Title, @BestOffer, @BidCount, @Brand, @Model, @MPN, @UPC, @BuyerProtection,
                    @CategoryID, @condition_id, @ConditionDescription,
                    @CountryFrom, @CountryShipsTo, @Description, @DispatchTimeMax,
                    @SellerFeedbackCount, @SellerFeedbackPercent, @GetItFast, @ListingType,
                    @LotSize, @PicturesURL, @PostalCode, @QuantityTotal, @QuantitySold,
                    @RefundOption, @RelistParentID, @ReturnsAcceptedOption,
                    @ReturnsWithinOption, @Revised, @SellerName, @ShippingCostPaidByOption,
                    @SubTitle, @TimeEnded, @TimePosted, @TopRatedListing, @SoldAsBin,
                    @BestOfferSold, @DurationSeconds, @PictureCount, @ProductReferenceID, @Price,
                    @BINSold, @AuctionSold, @BINNotSold, @AuctionNotSold,
                    @CommitToBuy, @ShippingType, @ShippingPrice, @Specifics,
                    @SearchName, @SellerFeedbackPrivate, @BuyerName, @BuyerFeedbackCount,
                    @BuyerFeedbackPercent, @BuyerFeedbackPrivate
                )
                ON CONFLICT (item_id) DO UPDATE SET
                    title = EXCLUDED.title,
                    best_offer = EXCLUDED.best_offer,
                    bid_count = EXCLUDED.bid_count,
                    price = EXCLUDED.price,
                    quantity_sold = EXCLUDED.quantity_sold,
                    time_ended = EXCLUDED.time_ended,
                    search_name = EXCLUDED.search_name,
                    best_offer_sold = EXCLUDED.best_offer_sold";

                for (int i = 0; i < items.Count; i += BatchSize)
                {
                    var batchItems = items.Skip(i).Take(BatchSize).ToList();
                    await using var batch = new NpgsqlBatch(conn);

                    foreach (var item in batchItems)
                    {
                        var batchCommand = new NpgsqlBatchCommand(sql);

                        batchCommand.Parameters.AddWithValue("@ItemId", item.ItemID);
                        batchCommand.Parameters.AddWithValue("@Title", item.Title ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@BestOffer", item.BestOffer);
                        batchCommand.Parameters.AddWithValue("@BidCount", item.BidCount);
                        batchCommand.Parameters.AddWithValue("@Brand", item.Brand ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@Model", item.Model ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@MPN", item.MPN ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@UPC", item.UPC ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@BuyerProtection", item.BuyerProtection ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@CategoryID", item.CategoryID);
                        batchCommand.Parameters.AddWithValue("@condition_id", item.ConditionID);
                        batchCommand.Parameters.AddWithValue("@ConditionDescription", item.ConditionDescription ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@CountryFrom", item.CountryFrom.ToString());
                        batchCommand.Parameters.AddWithValue("@CountryShipsTo", item.CountryShipsTo ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@Description", item.Description ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@DispatchTimeMax", item.DispatchTimeMax);
                        batchCommand.Parameters.AddWithValue("@SellerFeedbackCount", item.SellerFeedbackCount);
                        batchCommand.Parameters.AddWithValue("@SellerFeedbackPercent", item.SellerFeedbackPercent);
                        batchCommand.Parameters.AddWithValue("@GetItFast", item.GetItFast);
                        batchCommand.Parameters.AddWithValue("@ListingType", item.ListingType.ToString());
                        batchCommand.Parameters.AddWithValue("@LotSize", item.LotSize);
                        batchCommand.Parameters.AddWithValue("@PicturesURL", item.PicturesURL);
                        batchCommand.Parameters.AddWithValue("@PostalCode", item.PostalCode);
                        batchCommand.Parameters.AddWithValue("@QuantityTotal", item.QuantityTotal);
                        batchCommand.Parameters.AddWithValue("@QuantitySold", item.QuantitySold);
                        batchCommand.Parameters.AddWithValue("@RefundOption", item.RefundOption ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@RelistParentID", item.RelistParentID);
                        batchCommand.Parameters.AddWithValue("@ReturnsAcceptedOption", item.ReturnsAcceptedOption ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@ReturnsWithinOption", item.ReturnsWithinOption ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@Revised", item.Revised);
                        batchCommand.Parameters.AddWithValue("@SellerName", item.SellerName ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@ShippingCostPaidByOption", item.ShippingCostPaidByOption ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@SubTitle", item.SubTitle ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@TimeEnded", item.TimeEnded ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@TimePosted", item.TimePosted ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@TopRatedListing", item.TopRatedListing);
                        batchCommand.Parameters.AddWithValue("@SoldAsBin", item.SoldAsBin);
                        batchCommand.Parameters.AddWithValue("@BestOfferSold", item.BestOfferSold);
                        batchCommand.Parameters.AddWithValue("@DurationSeconds", item.DurationSeconds);
                        batchCommand.Parameters.AddWithValue("@PictureCount", item.PicturesCount);
                        batchCommand.Parameters.AddWithValue("@ProductReferenceID", item.ProductReferenceID ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@Price", item.Price);
                        batchCommand.Parameters.AddWithValue("@BINSold", item.BINSold);
                        batchCommand.Parameters.AddWithValue("@AuctionSold", item.AuctionSold);
                        batchCommand.Parameters.AddWithValue("@BINNotSold", item.BINNotSold);
                        batchCommand.Parameters.AddWithValue("@AuctionNotSold", item.AuctionNotSold);
                        batchCommand.Parameters.AddWithValue("@CommitToBuy", item.CommitToBuy);
                        batchCommand.Parameters.AddWithValue("@ShippingType", item.ShippingType.ToString());
                        batchCommand.Parameters.AddWithValue("@ShippingPrice", item.ShippingPrice);
                        batchCommand.Parameters.AddWithValue("@SearchName", item.SearchName ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@SellerFeedbackPrivate", item.SellerFeedbackPrivate);
                        batchCommand.Parameters.AddWithValue("@BuyerName", item.BuyerName ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@BuyerFeedbackCount", item.BuyerFeedbackCount ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@BuyerFeedbackPercent", item.BuyerFeedbackPercent ?? (object)DBNull.Value);
                        batchCommand.Parameters.AddWithValue("@BuyerFeedbackPrivate", item.BuyerFeedbackPrivate ?? (object)DBNull.Value);

                        var param = batchCommand.Parameters.Add("@Specifics", NpgsqlDbType.Jsonb);
                        param.Value = JsonSerializer.Serialize(item.Specifics ?? new Dictionary<string, string>());

                        batch.BatchCommands.Add(batchCommand);
                    }

                    await batch.ExecuteNonQueryAsync();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
        }

    }
}
