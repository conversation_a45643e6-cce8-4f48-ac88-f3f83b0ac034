using CsvHelper;
using eBay.Service.Core.Sdk;
using ItemScraper.Configuration;
using ItemScraper.Services;
using System.Globalization;
using Npgsql;

namespace ItemScraper
{
    internal class Program
    {
        public static string DataAnalysisDateStated = "2025_03_21";
        internal static string EbayDomain = "www.ebay.com";
        private const string OldItemsIdsFilePath = "oldItemsIds.txt"; // File to store old item IDs

        static async Task Main(string[] args)
        {
            await LoadOldItemsFromFile();

            int maxDegreeOfParallelism = 2;

            maxDegreeOfParallelism = ParseCmdThreadCount(args, maxDegreeOfParallelism);

            var config = ScraperConfig.SoldItems;
            if (config.AnalysisType == AnalysisType.SoldItems)
            {
                if (ScraperConfig.SoldItemsMotors.DatabaseConfigurationName == config.DatabaseConfigurationName)
                {
                    SerpUrlGenerator.EbayMaxResultsCount = 9840;
                }
                else
                {
                    SerpUrlGenerator.EbayMaxResultsCount = 400000;
                }
            }
            else
            {
                SerpUrlGenerator.EbayMaxResultsCount = 9840;
            }
            var logger = new LoggingService();
            var scheduler = new SchedulerService(config, logger);

            // Initialize API context
            var apiContext = InitializeApiContext(config);
            var itemValidator = new ItemValidator(logger, config.AnalysisType);
            await itemValidator.FetchExistingItemsCache(config);
            // Initialize services with proper dependencies
            var itemProcessor = new ItemProcessor(config, logger, apiContext, itemValidator);
            var serpProcessor = new SerpProcessor(config, itemProcessor, itemValidator);
            var scraper = new ScrapingService(config, logger, itemProcessor, serpProcessor, maxDegreeOfParallelism);

            logger.LogMessage("Started");

            // Commenting out old/unused logic
            //await FixBuyerNamesFromFile(itemProcessor);

            //var categoryIds = new List<int>() { 42894, 78191, 78192, };
            //foreach (var categoryId in categoryIds)
            //{
            //    await GetDataAnalysisFromItemIds(itemProcessor, categoryId);
            //}
            //return;

            int lastTermIndex = await scraper.LoadLastTermIndex();
            while (true)
            {
                await scraper.ProcessTerms(lastTermIndex);
                await scheduler.WaitForNextRun();
                lastTermIndex = 0;
            }

            // Start the new database processing loop
            //await FixBuyersFromDatabaseLoop(config, itemProcessor, logger);
        }

        private static async Task LoadOldItemsFromFile()
        {
            // Load oldItemsIds from file
            if (File.Exists(OldItemsIdsFilePath))
            {
                try
                {
                    var lines = await File.ReadAllLinesAsync(OldItemsIdsFilePath);
                    oldItemsIds = new HashSet<Int64>();
                    foreach (var line in lines)
                    {
                        if (long.TryParse(line, out var id))
                        {
                            oldItemsIds.Add(id);
                        }
                        else
                        {
                            Console.WriteLine($"Warning: Could not parse '{line}' as Int64 from {OldItemsIdsFilePath}. Skipping.");
                        }
                    }
                    Console.WriteLine($"Loaded {oldItemsIds.Count} item IDs from {OldItemsIdsFilePath}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error loading {OldItemsIdsFilePath}: {ex.Message}. Starting with an empty set.");
                    oldItemsIds = new HashSet<Int64>();
                }
            }
            else
            {
                Console.WriteLine($"{OldItemsIdsFilePath} not found. Starting with an empty set.");
                oldItemsIds = new HashSet<Int64>();
            }
        }

        private static int ParseCmdThreadCount(string[] args, int maxDegreeOfParallelism)
        {
            // Parse command-line arguments for -t
            for (int i = 0; i < args.Length; i++)
            {
                if ((args[i] == "-t" || args[i] == "--threads") && i + 1 < args.Length)
                {
                    if (int.TryParse(args[i + 1], out int parsedValue) && parsedValue > 0)
                    {
                        maxDegreeOfParallelism = parsedValue;
                        Console.WriteLine($"Using MaxDegreeOfParallelism: {maxDegreeOfParallelism}");
                        i++;
                    }
                    else
                    {
                        Console.WriteLine($"Warning: Invalid value '{args[i + 1]}' provided for -t. Using default: {maxDegreeOfParallelism}");
                        i++;
                    }
                }
            }

            return maxDegreeOfParallelism;
        }

        private static ApiContext InitializeApiContext(ScraperConfig config)
        {
            var apiContext = EbayApi.GetApiContext(config.ApiToken);
            apiContext.ApiCredential.eBayToken = config.ApiToken;
            return apiContext;
        }

        private static async Task FixBuyerNamesFromFile(ItemProcessor itemProcessor)
        {
            var serpItems = new List<SerpParser.SerpItem>();

            // Read from CSV file
            using (var reader = new StreamReader("C:\\Temp\\_Work\\fix_buyer_names_ids.csv"))
            using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
            {
                var records = csv.GetRecords<dynamic>();
                foreach (var record in records)
                {
                    var itemId = long.Parse(record.item_id.ToString());
                    //var fetchTime = DateTime.Parse(record.fetch_time.ToString());

                    // Create SerpItem with minimal required data
                    var serpItem = new SerpParser.SerpItem(itemId, DateTime.UtcNow, 0, 0, false);
                    serpItems.Add(serpItem);
                }
            }

            const int batchSize = 1000;
            for (int i = 0; i < serpItems.Count; i += batchSize)
            {
                var batch = serpItems.Skip(i).Take(batchSize).ToList();
                await itemProcessor.ProcessBatchFromFile(batch, "");
            }
        }

        private static async Task GetDataAnalysisFromItemIds(ItemProcessor itemProcessor, int categoryId)
        {
            Console.WriteLine("categoryID:" + categoryId);
            var searchName = categoryId.ToString();
            ScraperConfig.DataAnalysisTableName = $"_{DateTime.UtcNow:yyyy_MM_dd}_{searchName.Replace(" ", "_")}";

            await itemProcessor.Initialize();
            List<SerpParser.SerpItem> serpItems;

            // Read from CSV file
            //serpItems = GetSerpItemsFromFile();

            serpItems = await GetSerpItemsFromDb(categoryId);
            Console.WriteLine("serpItems:" + serpItems.Count);
            const int batchSize = 1000;
            for (int i = 0; i < serpItems.Count; i += batchSize)
            {
                var batch = serpItems.Skip(i).Take(batchSize).ToList();
                await itemProcessor.ProcessBatchFromFile(batch, searchName);
            }
        }

        private static async Task<List<SerpParser.SerpItem>> GetSerpItemsFromDb(int categoryId)
        {
            var soldItemsFetcher = new SoldItemsFetcher();
            var leaf_categories = await soldItemsFetcher.GetLeafCategoryIds(categoryId);
            var itemIds = await soldItemsFetcher.FetchSoldItemIds(leaf_categories);
            var serpItems = itemIds.Select(itemId => new SerpParser.SerpItem(itemId, DateTime.UtcNow, 0, 0, false)).ToList();
            return serpItems;
        }

        private static List<SerpParser.SerpItem> GetSerpItemsFromFile()
        {
            List<SerpParser.SerpItem> serpItems;
            using (var reader = new StreamReader("_itemids_data_analysis.csv"))
            using (var csv = new CsvReader(reader, CultureInfo.InvariantCulture))
            {
                IEnumerable<dynamic> records = csv.GetRecords<dynamic>();
                var itemIds = records.Select(record => long.Parse(record.item_id.ToString()));
                serpItems = itemIds.Select(itemId => new SerpParser.SerpItem(itemId, DateTime.UtcNow, 0, 0, false)).ToList();
            }

            return serpItems;
        }
        // Closing brace for Program class moved below FixBuyersFromDatabaseLoop
        public static HashSet<Int64> oldItemsIds = new HashSet<Int64>();
        private static async Task FixBuyersFromDatabaseLoop(ScraperConfig config, ItemProcessor itemProcessor, LoggingService logger)
        {
            logger.LogMessage("Starting database processing loop.");
            var dbConfig = DatabaseConfigurations.GetConfiguration(config.DatabaseConfigurationName);
            if (dbConfig == null)
            {
                // Pass a new exception for configuration errors
                logger.LogError($"Database configuration '{config.DatabaseConfigurationName}' not found.", new InvalidOperationException($"Database configuration '{config.DatabaseConfigurationName}' not found."));
                return;
            }

            const int batchSize = 1000;
            const string buyerNameFilter = "^^";
            var startTimeFilter = new DateTime(2025, 2, 11, 0, 0, 0, DateTimeKind.Utc); // Ensure UTC
            var sleepTime = TimeSpan.FromSeconds(2);
            var dbItemsSeen = new List<long>();
            while (true) // Outer infinite loop
            {
                logger.LogMessage("Starting new database query cycle.");
                long offset = 0;
                bool processedItemsInCycle = false;

                while (true) // Inner batching loop
                {
                    //offset += 100;
                    var itemIds = new List<long>();
                    try
                    {
                        using (var connection = new NpgsqlConnection(dbConfig.GetConnectionString()))
                        {
                            await connection.OpenAsync();

                            // Adjust table and column names if necessary
                            var sql = $@"
                            SELECT item_id
                            FROM public.sold_items
                            WHERE start_time > @startTime
                            AND buyer_name = @buyerName                           
                            LIMIT @limit ";
                            //offset @offset";

                            using (var cmd = new NpgsqlCommand(sql, connection))
                            {
                                cmd.CommandTimeout = 300; // Set command timeout to 5 minutes (300 seconds)
                                cmd.Parameters.AddWithValue("startTime", startTimeFilter);
                                cmd.Parameters.AddWithValue("buyerName", buyerNameFilter);
                                cmd.Parameters.AddWithValue("limit", batchSize);
                                //cmd.Parameters.AddWithValue("offset", offset);

                                logger.LogMessage($"Executing query with OFFSET {offset} (Timeout: {cmd.CommandTimeout}s)...");
                                using (var reader = await cmd.ExecuteReaderAsync())
                                {
                                    while (await reader.ReadAsync())
                                    {
                                        var itemId = reader.GetInt64(0);
                                        if (!oldItemsIds.Contains(itemId))
                                        {
                                            oldItemsIds.Add(itemId);
                                            itemIds.Add(itemId);
                                        }
                                    }
                                }
                                logger.LogMessage($"Fetched {itemIds.Count} items.");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        // Pass the caught exception 'ex'
                        logger.LogError($"Error fetching items from database: {ex.Message}", ex);
                        logger.LogMessage($"Waiting {sleepTime.TotalMinutes} minutes before retrying database connection...");
                        await Task.Delay(sleepTime); // Wait before retrying connection
                        continue; // Retry the current batch
                    }

                    itemIds = itemIds
                        .Where(itemId => !dbItemsSeen.Contains(itemId))
                        .ToList();
                    if (itemIds.Count < 1000)
                    {

                    }
                    dbItemsSeen.AddRange(itemIds);

                    if (itemIds.Count > 0)
                    {
                        processedItemsInCycle = true;
                        var serpItems = itemIds.Select(itemId => new SerpParser.SerpItem(itemId, DateTime.UtcNow, 0, 0, false)).ToList();

                        logger.LogMessage($"Processing batch of {serpItems.Count} items (Offset: {offset})...");
                        try
                        {
                            // Using ProcessBatchFromFile as it takes List<SerpItem>.
                            // The 'searchName' parameter might not be relevant here, passing empty string.
                            await itemProcessor.ProcessBatchFromFile(serpItems, "DatabaseFetch");
                            logger.LogMessage($"Finished processing batch (Offset: {offset}).");

                            // Save oldItemsIds to file
                            try
                            {
                                await File.WriteAllLinesAsync(OldItemsIdsFilePath, oldItemsIds.Select(id => id.ToString()));
                                logger.LogMessage($"Saved {oldItemsIds.Count} item IDs to {OldItemsIdsFilePath}");
                            }
                            catch (Exception ex)
                            {
                                logger.LogError($"Error saving {OldItemsIdsFilePath}: {ex.Message}", ex);
                            }
                        }
                        catch (Exception ex)
                        {
                            // Pass the caught exception 'ex'
                            logger.LogError($"Error processing batch (Offset: {offset}): {ex.Message}", ex);
                            // Decide if you want to retry the batch or skip it
                            // For now, we'll log and continue to the next offset
                        }

                        // offset += itemIds.Count; // Move to the next offset

                        // Wait 10 minutes AFTER processing a batch before fetching the next one
                        logger.LogMessage($"Waiting {sleepTime.TotalMinutes} minutes before fetching next batch...");
                        await Task.Delay(sleepTime);
                    }
                    else
                    {
                        logger.LogMessage("No more items found in this cycle.");
                        break;
                    }

                }

                // If no items were processed in the entire cycle, wait before starting the next cycle.
                // If items *were* processed, the delay already happened after the last batch.
                // Also, save one last time at the end of a full cycle if any items were processed.
                if (processedItemsInCycle)
                {
                    try
                    {
                        await File.WriteAllLinesAsync(OldItemsIdsFilePath, oldItemsIds.Select(id => id.ToString()));
                        logger.LogMessage($"Saved {oldItemsIds.Count} item IDs to {OldItemsIdsFilePath} at the end of the cycle.");
                    }
                    catch (Exception ex)
                    {
                        logger.LogError($"Error saving {OldItemsIdsFilePath} at the end of the cycle: {ex.Message}", ex);
                    }
                }
                else
                {
                    logger.LogMessage($"No items found matching criteria. Waiting {sleepTime.TotalMinutes} minutes before starting next cycle...");
                    await Task.Delay(sleepTime);
                }
            }
        }
    }
}
