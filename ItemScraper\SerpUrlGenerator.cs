﻿using System.Diagnostics;
using System.Text.RegularExpressions;
using HtmlAgilityPack;
using ItemScraper.Terms;

namespace ItemScraper
{
    public abstract class SerpUrlGenerator
    {
        public static int RssUrlsCount;
        public static decimal EbayMaxPrice = 100000000;
        public static double EbayMaxResultsCount { get; set; } = 400000; //for two days set to 400000

        public static async Task<List<string>> GetSerpUrlsList(SearchTerm term)
        {
            RssUrlsCount = 0;
            var estimatedItemsCount = await GetResultsCount(term.BasicSerpUrl);
            var maxItemsLimit = term.MaxItems;
            if (estimatedItemsCount < term.MaxItems)
                maxItemsLimit = estimatedItemsCount;

            List<string> serpUrlsList = new List<string>();

            if (maxItemsLimit < EbayMaxResultsCount)
            {
                var maxPages = Math.Min(42, Math.Ceiling((double)maxItemsLimit / 240) + 1);
                for (int pageNum = 1; pageNum < maxPages; pageNum++)
                {
                    serpUrlsList.Add(term.BasicSerpUrl + "&_pgn=" + pageNum);
                }

                RssUrlsCount = serpUrlsList.Count;
                return serpUrlsList;
            }

            await GenerateSerpUrlsRecursive(term.PriceMin, 5000, term.BasicSerpUrl, serpUrlsList, maxItemsLimit);
            await GenerateSerpUrlsRecursive((decimal)5000.01, term.PriceMax, term.BasicSerpUrl, serpUrlsList, maxItemsLimit);
            serpUrlsList.Reverse();
            RssUrlsCount = serpUrlsList.Count;
            return serpUrlsList;
        }

        public static async Task GenerateSerpUrlsRecursive(decimal minPrice, decimal maxPrice, string basicRssUrl, List<string> urlList, int userMaxItemsLimit = 0)
        {

            if (userMaxItemsLimit != 0 && userMaxItemsLimit < EbayMaxResultsCount)
                //if (userMaxItemsLimit < ebayMaxItemsLimit)
            {
                var maxPages = Math.Min(42, Math.Ceiling((double)userMaxItemsLimit / 240) + 1);
                if (minPrice + (decimal)0.01 <= maxPrice)
                    for (int pageNum = 1; pageNum < maxPages; pageNum++)
                    {
                        urlList.Add($"{basicRssUrl}&_udlo={minPrice + (decimal)0.01}&_udhi={maxPrice}&_pgn={pageNum}");
                    }

                return;
            }

            var priceBinsCount = 5;

            var step = (maxPrice - minPrice) / priceBinsCount;
            step = Math.Ceiling(step * 100) * (decimal) 0.01;
            var originalMaxPrice = maxPrice;

            maxPrice = minPrice;
            for (var i = 0; i < priceBinsCount; i++)
            {
                minPrice = maxPrice;
                maxPrice = minPrice + step;
                if (originalMaxPrice - maxPrice < 0)
                    maxPrice = originalMaxPrice;
                var url = $"{basicRssUrl}&_udlo={(minPrice + (decimal)0.01)}&_udhi={maxPrice}";
                var html = await Network.FetchUrlAsync(url);

                var resultsCountTmp = ParseSerpItemsCount(html);
                var logtxt = $"Min: {minPrice + (decimal)0.01}\tMax: {maxPrice}\tCnt: {resultsCountTmp}";
                Debug.WriteLine(logtxt);
                if (resultsCountTmp < EbayMaxResultsCount || step < (decimal)0.1)
                {
                    var maxPages = Math.Min(42, Math.Ceiling((double)resultsCountTmp / 240) + 1);
                    if (minPrice + (decimal)0.01 <= maxPrice)
                        for (int pageNum = 1; pageNum < maxPages; pageNum++)
                        {
                            urlList.Add($"{basicRssUrl}&_udlo={minPrice + (decimal)0.01}&_udhi={maxPrice}&_pgn={pageNum}");
                        }
                }
                else
                {
                    await GenerateSerpUrlsRecursive(minPrice, maxPrice, basicRssUrl, urlList);
                }
            }
        }

        public static List<List<string>> GenerateSerpUrlsInPriceRange(decimal minPrice, decimal maxPrice, string basicRssUrl)
        {
            var urlListSets = new List<List<string>>();
            var priceBinsCount = 5;
            var priceStep = (maxPrice - minPrice) / priceBinsCount;
            priceStep = Math.Ceiling(priceStep * 100) * (decimal)0.01;
            var originalMaxPrice = maxPrice;

            maxPrice = minPrice;
            for (var i = 0; i < priceBinsCount; i++)
            {
                minPrice = maxPrice;
                maxPrice = minPrice + priceStep;
                if (originalMaxPrice - maxPrice < 0)
                    maxPrice = originalMaxPrice;

                var maxPages = Math.Min(42, Math.Ceiling(EbayMaxResultsCount / 240) + 1);
                var urlList = new List<string>();
                if (minPrice + (decimal)0.01 <= maxPrice)
                    for (int pageNum = 1; pageNum < maxPages; pageNum++)
                    {
                        urlList.Add($"{basicRssUrl}&_udlo={minPrice + (decimal)0.01}&_udhi={maxPrice}&_pgn={pageNum}");
                    }

                urlListSets.Add(urlList);
            }

            return urlListSets;
        }

        public static List<string> GenerateSerpUrls(string termBasicSerpUrl, decimal minPrice, decimal maxPrice)
        {
            var serpUrls = new List<string>();
            var maxPages = Math.Min(42, Math.Ceiling(EbayMaxResultsCount / 240) + 1);
            if (minPrice + (decimal)0.01 <= maxPrice)
                for (int pageNum = 1; pageNum < maxPages; pageNum++)
                {
                    serpUrls.Add($"{termBasicSerpUrl}&_udlo={minPrice + (decimal)0.01}&_udhi={maxPrice}&_pgn={pageNum}");
                }

            return serpUrls;
        }

        public static List<string> GenerateSerpUrls(string termBasicSerpUrl)
        {
            var serpUrls = new List<string>();
            var maxPages = Math.Min(42, Math.Ceiling(EbayMaxResultsCount / 240) + 1);
            for (int pageNum = 1; pageNum < maxPages; pageNum++)
            {
                serpUrls.Add($"{termBasicSerpUrl}&_pgn={pageNum}");
            }

            return serpUrls;
        }

        public static async Task<int> GetResultsCount(string baseSerpUrl)
        {
            var html = await Network.FetchUrlAsync(baseSerpUrl);
            return ParseSerpItemsCount(html);
        }

        public static int ParseSerpItemsCount(string html)
        {
            if (html == null)
                return 0;

            var document1 = new HtmlDocument();

            document1.LoadHtml(html);

            var resultsNode = document1.DocumentNode.SelectSingleNode("//h1[@ class='srp-controls__count-heading']");

            if (resultsNode == null) 
                return 0;

            resultsNode.Descendants().Where(n => n.NodeType == HtmlAgilityPack.HtmlNodeType.Comment).ToList().ForEach(n => n.Remove());
            var count = RegexValue(resultsNode.InnerText.Trim().Replace(",", ""), "([0-9]+)");
            if (Int32.TryParse(count, out var countResult))
                return countResult;

            return 0;
        }

        public static string RegexValue(string partPageHtml, string regex)
        {
            if (partPageHtml == null)
                return "";

            var match = Regex.Match(partPageHtml, regex, RegexOptions.Compiled);
            var str = match.Success ? match.Groups[1].Value : "";
            return str;
        }

        public void Start(List<SearchTerm> terms)
        {
            var itemIdFolder = "ItemIds";
            Task.Run(async () =>
            {
                foreach (var searchTerm in terms)
                {
                    var rssUrls = await GetSerpUrlsList(searchTerm);
                    File.WriteAllLines(Path.Combine(itemIdFolder, $"{searchTerm.Alias}.txt"), rssUrls);
                }
            }
                );
        }

        public static List<PriceRange> AggregatePriceRanges(List<PriceRange> elements)
        {
            List<PriceRange> aggregatedRanges = new List<PriceRange>();
            int sumCount = 0; // Track the sum of counts in the current aggregation
            PriceRange currentRange = null; // Track the current price range being aggregated

            foreach (var element in elements)
            {
                // Check if adding the current element's count would exceed 10,000
                if (sumCount + element.Count >= EbayMaxResultsCount)
                {
                    // If there is an ongoing range, add it to the list before starting a new one
                    if (currentRange != null)
                    {
                        aggregatedRanges.Add(currentRange);
                        sumCount = 0; // Reset sumCount for the new aggregation
                    }

                    // Start a new price range with the current element
                    currentRange = new PriceRange {
                        Min = element.Min,
                        Max = element.Max,
                        Count = element.Count
                    };

                    // Check if the new range's count is less than 10000 to avoid immediate completion
                    if (element.Count < EbayMaxResultsCount)
                    {
                        sumCount = element.Count;
                    }
                    else
                    {
                        // If the single range itself exceeds or equals 10000, add and reset immediately
                        aggregatedRanges.Add(currentRange);
                        currentRange = null;
                    }
                }
                else
                {
                    // If this is the first element or a continuation that doesn't exceed 10000
                    if (currentRange == null)
                    {
                        currentRange = new PriceRange {
                            Min = element.Min,
                            Max = element.Max,
                            Count = element.Count
                        };
                        sumCount = element.Count;
                    }
                    else
                    {
                        // Aggregate with the current range
                        currentRange.Max = element.Max;
                        currentRange.Count += element.Count;
                        sumCount += element.Count;
                    }
                }
            }

            // Don't forget to add the last range if it exists
            if (currentRange != null)
            {
                aggregatedRanges.Add(currentRange);
            }

            return aggregatedRanges;
        }

        public static List<PriceRange> ParseHtmlToPriceRanges(string html)
        {
            var doc = new HtmlDocument();
            doc.LoadHtml(html);
            var spans = doc.DocumentNode.SelectNodes("//div[contains(text(), 'Available inventory')]/following-sibling::div/span");
            if (spans == null)
            {

            }

            var priceRanges = new List<PriceRange>();

            if (spans != null)
            {
                foreach (var span in spans)
                {
                    int min = int.Parse(span.GetAttributeValue("data-min", "0"));
                    int max = int.Parse(span.GetAttributeValue("data-max", EbayMaxPrice).ToString());
                    int count = int.Parse(span.GetAttributeValue("data-count", "0"));

                    priceRanges.Add(new PriceRange {
                        Min = min,
                        Max = max,
                        Count = count
                    });
                }
            }

            return priceRanges;
        }
    }

    public class PriceRange
    {
        public decimal Min { get; set; }
        public decimal Max { get; set; }
        public int Count { get; set; }

        public override string ToString()
        {
            return $"Min:{Min} Max:{Max} Count:{Count}";
        }
    }
}