﻿using HtmlAgilityPack;
using System.Text;

public static class HtmlToText
{
    public static string ConvertHtml(string html)
    {
        if (string.IsNullOrWhiteSpace(html))
            return string.Empty;

        HtmlDocument document = new HtmlDocument();
        document.LoadHtml(html);

        StringBuilder textBuilder = new StringBuilder();
        ExtractContent(document.DocumentNode, textBuilder);

        return textBuilder.ToString().Trim();
    }

    private static void ExtractContent(HtmlNode node, StringBuilder textBuilder)
    {
        // Define tags to ignore
        var tagsToIgnore = new HashSet<string> { "script", "style", "nav", "meta", "link", "head" };

        foreach (var childNode in node.ChildNodes)
        {
            if (tagsToIgnore.Contains(childNode.Name) || childNode.NodeType == HtmlNodeType.Comment)
                continue;

            switch (childNode.NodeType)
            {
                case HtmlNodeType.Text:
                    var text = childNode.InnerText.Trim();
                    if (!string.IsNullOrWhiteSpace(text))
                    {
                        textBuilder.Append(HtmlEntity.DeEntitize(text) + " ");
                    }
                    break;

                case HtmlNodeType.Element:
                    if (childNode.Name == "br" || childNode.Name == "p" || childNode.Name == "div" || childNode.Name == "li")
                    {
                        textBuilder.AppendLine();
                    }

                    ExtractContent(childNode, textBuilder); // Recursively process child nodes
                    break;
            }
        }
    }
}