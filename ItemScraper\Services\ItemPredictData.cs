using eBay.Service.Core.Soap;

namespace ItemScraper.Services
{
    public class ItemPredictData
    {
        
        public string Title { get; set; }
        public bool BestOffer { get; set; }
        public int BidCount { get; set; }
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public string? MPN { get; set; }
        public string? UPC { get; set; }
        public string? ProductReferenceID { get; set; }

        public string? BuyerProtection { get; set; }
        public int CategoryID { get; set; }
        public int ConditionID { get; set; }
        public string ConditionDescription { get; set; }
        public string CountryFrom { get; set; }
        public string CountryShipsTo { get; set; }
        public string Description { get; set; }
        public int DispatchTimeMax { get; set; }

        public bool GetItFast { get; set; }
        public long ItemID { get; set; }
        public ListingTypeCodeType ListingType { get; set; }
        public int LotSize { get; set; }
        public string PicturesURL { get; set; }
        public int PicturesCount { get; set; }
        public string PostalCode { get; set; }
        public int QuantityTotal { get; set; }
        public int QuantitySold { get; set; }
        public string? RefundOption { get; set; }
        public long RelistParentID { get; set; }        
        public string? ReturnsAcceptedOption { get; set; }
        public string? ReturnsWithinOption { get; set; }
        public bool Revised { get; set; }
        public string SellerName { get; set; }
        public int SellerFeedbackCount { get; set; }
        public float SellerFeedbackPercent { get; set; }
        public bool SellerFeedbackPrivate { get; set; }
        public string? BuyerName { get; set; }
        public int? BuyerFeedbackCount { get; set; }
        public float? BuyerFeedbackPercent { get; set; }
        public bool? BuyerFeedbackPrivate { get; set; }
        
        public string? ShippingCostPaidByOption { get; set; }
        public string SubTitle { get; set; }
        public System.DateTime? TimeEnded { get; set; }
        public System.DateTime? TimePosted { get; set; }
        public bool TopRatedListing { get; set; }
        public bool SoldAsBin { get; set; }
        public bool BestOfferSold { get; set; }
        public decimal DurationSeconds { get; set; }
        public decimal Price { get; set; }
        public bool BINSold { get; set; }
        public bool AuctionSold { get; set; }
        public bool BINNotSold { get; set; }
        public bool AuctionNotSold { get; set; }
        public bool CommitToBuy { get; set; }
        public string Comment { get; set; }
        public ShippingTypeCodeType ShippingType { get; set; }
        public decimal? ShippingPrice { get; set; }
        public Dictionary<string, string> Specifics { get; set; }
        public string SearchName { get; set; }
        
    }
}
